# Smart Farming App - Setup Guide

## 🚀 Quick Start Guide

Your Smart Farming App with advanced camera features is ready to run! Follow these steps to get it running on an emulator or device.

## ✅ Current Status

✅ **Metro Bundler**: Running successfully  
✅ **Dependencies**: Installed  
✅ **Project Structure**: Complete  
✅ **Camera Implementation**: Ready  

## 📱 Running on Emulator/Device

### Option 1: iOS Simulator (macOS only)

1. **Install Xcode** (if not already installed):
   ```bash
   # Download from Mac App Store or Apple Developer
   ```

2. **Install iOS dependencies**:
   ```bash
   cd ios
   pod install
   cd ..
   ```

3. **Run on iOS Simulator**:
   ```bash
   npx react-native run-ios
   ```

### Option 2: Android Emulator

1. **Install Android Studio** (if not already installed):
   - Download from https://developer.android.com/studio
   - Install Android SDK and create a virtual device

2. **Start Android Emulator**:
   ```bash
   # Open Android Studio > AVD Manager > Start emulator
   # Or use command line:
   emulator -avd YOUR_AVD_NAME
   ```

3. **Run on Android**:
   ```bash
   npx react-native run-android
   ```

### Option 3: Physical Device

#### iOS Device:
1. Connect iPhone via USB
2. Trust the computer on device
3. Run: `npx react-native run-ios --device`

#### Android Device:
1. Enable Developer Options and USB Debugging
2. Connect via USB
3. Run: `npx react-native run-android`

## 🛠️ Development Environment Setup

### Prerequisites

1. **Node.js 18+**
   ```bash
   node --version  # Should be 18+
   ```

2. **React Native CLI**
   ```bash
   npm install -g @react-native-community/cli
   ```

3. **Platform-specific tools**:
   - **iOS**: Xcode, CocoaPods
   - **Android**: Android Studio, Java JDK

### Environment Variables

Add to your shell profile (`.bashrc`, `.zshrc`):

```bash
# Android
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools

# Java (for Android)
export JAVA_HOME=/Library/Java/JavaVirtualMachines/jdk-11.0.x.jdk/Contents/Home
```

## 🎯 Testing Camera Features

Once the app is running, you can test:

### 1. Camera Modes
- **Plant Analysis**: Tap camera icon → Select "Plant Analysis"
- **Soil Analysis**: Switch to "Soil Analysis" mode
- **Field Survey**: Use "Field Survey" for wide shots
- **Hyperspectral**: Advanced analysis mode

### 2. Camera Controls
- **Flash**: Tap flash icon (auto/on/off)
- **Switch Camera**: Front/back toggle
- **Zoom**: Pinch to zoom
- **Capture**: Large capture button

### 3. AI Analysis
- Take a photo in any mode
- View instant AI analysis results
- See recommendations and confidence scores
- Check GPS location data

## 🔧 Troubleshooting

### Common Issues

1. **Metro bundler not starting**:
   ```bash
   npx react-native start --reset-cache
   ```

2. **iOS build fails**:
   ```bash
   cd ios && pod install && cd ..
   npx react-native run-ios --clean
   ```

3. **Android build fails**:
   ```bash
   cd android && ./gradlew clean && cd ..
   npx react-native run-android
   ```

4. **Camera permissions**:
   - iOS: Check Info.plist for camera usage description
   - Android: Verify permissions in AndroidManifest.xml

### Performance Tips

1. **Enable Hermes** (already configured):
   - Faster startup times
   - Reduced memory usage

2. **Optimize images**:
   - App automatically optimizes based on capture mode
   - Adjust quality in Settings

3. **GPS accuracy**:
   - Balance accuracy vs battery life in Settings
   - Use high accuracy for field mapping

## 📊 App Features Overview

### 🏠 Home Screen
- Dashboard with farming statistics
- Quick actions for common tasks
- Recent activity and recommendations

### 📸 Camera Screen
- Multi-mode capture system
- Real-time GPS tagging
- Professional camera controls
- Instant AI analysis

### 🌾 Field Profiles
- GPS-mapped field management
- Crop type and planting tracking
- Historical analysis records

### 📈 Analytics
- Trend analysis and reporting
- Disease outbreak tracking
- Performance metrics

### ⚙️ Settings
- Camera and GPS preferences
- Notification settings
- Data sync and backup

## 🚀 Next Steps

1. **Test on physical device** for full camera functionality
2. **Add real AI models** for production use
3. **Integrate weather APIs** for enhanced recommendations
4. **Set up cloud backend** for data synchronization
5. **Deploy to app stores** for distribution

## 📱 Demo Features

The app includes comprehensive demo data showing:
- Disease detection results
- Soil analysis reports
- Crop health assessments
- Field mapping capabilities
- Analytics dashboards

## 🔗 Useful Commands

```bash
# Start Metro bundler
npx metro start

# Run on iOS
npx react-native run-ios

# Run on Android
npx react-native run-android

# Clean and rebuild
npx react-native start --reset-cache

# Check React Native info
npx react-native info

# Debug on device
npx react-native log-ios    # iOS logs
npx react-native log-android # Android logs
```

## 📞 Support

If you encounter any issues:
1. Check the troubleshooting section above
2. Verify your development environment setup
3. Ensure all dependencies are properly installed
4. Test on a physical device for camera features

---

**Your Smart Farming App is ready to revolutionize agriculture! 🌱📱**

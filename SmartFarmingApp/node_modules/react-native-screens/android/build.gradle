import com.android.Version
import groovy.json.JsonSlurper

buildscript {
    ext {
        rnsDefaultTargetSdkVersion = 34
        rnsDefaultCompileSdkVersion = 34
        rnsDefaultMinSdkVersion = 21
        rnsDefaultKotlinVersion = '1.8.0'
    }
    ext.safeExtGet = {prop, fallback ->
        rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath('com.android.tools.build:gradle:8.2.1')
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:${safeExtGet('kotlinVersion', rnsDefaultKotlinVersion)}"
        classpath "com.diffplug.spotless:spotless-plugin-gradle:6.25.0"
    }
}

def isRunningInContextOfScreensRepo() {
    return project == rootProject
}

def isNewArchitectureEnabled() {
    // To opt-in for the New Architecture, you can either:
    // - Set `newArchEnabled` to true inside the `gradle.properties` file
    // - Invoke gradle with `-newArchEnabled=true`
    // - Set an environment variable `ORG_GRADLE_PROJECT_newArchEnabled=true`
    return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

// spotless is only accessible within react-native-screens repo
if (isRunningInContextOfScreensRepo()) {
    apply from: 'spotless.gradle'
}

if (isNewArchitectureEnabled()) {
    apply plugin: "com.facebook.react"
}
apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

def reactNativeArchitectures() {
    def value = project.getProperties().get("reactNativeArchitectures")
    return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}

def safeAppExtGet(prop, fallback) {
    def appProject = rootProject.allprojects.find { it.plugins.hasPlugin('com.android.application') }
    appProject?.ext?.has(prop) ? appProject.ext.get(prop) : fallback
}

def IS_NEW_ARCHITECTURE_ENABLED = isNewArchitectureEnabled()

android {
    compileSdkVersion safeExtGet('compileSdkVersion', rnsDefaultCompileSdkVersion)
    namespace "com.swmansion.rnscreens"

    // Used to override the NDK path/version on internal CI or by allowing
    // users to customize the NDK path/version from their root project (e.g. for M1 support)
    if (rootProject.hasProperty("ndkPath")) {
        ndkPath rootProject.ext.ndkPath
    }
    if (rootProject.hasProperty("ndkVersion")) {
        ndkVersion rootProject.ext.ndkVersion
    }

    defaultConfig {
        minSdkVersion safeExtGet('minSdkVersion', rnsDefaultMinSdkVersion)
        targetSdkVersion safeExtGet('targetSdkVersion', rnsDefaultTargetSdkVersion)
        versionCode 1
        versionName "1.0"
        buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", IS_NEW_ARCHITECTURE_ENABLED.toString()
        ndk {
            abiFilters (*reactNativeArchitectures())
        }
        externalNativeBuild {
            cmake {
                arguments "-DANDROID_STL=c++_shared",
                        "-DRNS_NEW_ARCH_ENABLED=${IS_NEW_ARCHITECTURE_ENABLED}"
            }
        }
    }
    buildFeatures {
        prefab true
        buildConfig true
    }
    externalNativeBuild {
        cmake {
            path "CMakeLists.txt"
        }
    }
    lintOptions {
        abortOnError false
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    packagingOptions {
        // For some reason gradle only complains about the duplicated version of libreact_render libraries
        // while there are more libraries copied in intermediates folder of the lib build directory, we exclude
        // only the ones that make the build fail (ideally we should only include librnscreens_modules but we
        // are only allowed to specify exclude patterns)
        excludes = [
                "META-INF",
                "META-INF/**",
                "**/libjsi.so",
                "**/libc++_shared.so",
                "**/libreact_render*.so",
                "**/libreactnativejni.so",
                "**/libreact_performance_timeline.so",
                // In 0.76 multiple react-native's libraries were merged and these are the main new artifacts we're using.
                // Some of above lib* names could be removed after we remove support for 0.76.
                // https://github.com/facebook/react-native/pull/43909
                // https://github.com/facebook/react-native/pull/46059
                "**/libfbjni.so", 
                "**/libreactnative.so"
        ]
    }
    sourceSets.main {
        ext.androidResDir = "src/main/res"
        java {
            if (IS_NEW_ARCHITECTURE_ENABLED) {
                srcDirs += [
                    "src/fabric/java",
                ]
            } else {
                srcDirs += [
                    "src/paper/java",
                ]
            }
        }
        res {
            if (safeExtGet('compileSdkVersion', rnsDefaultCompileSdkVersion) >= 33) {
                srcDirs = ["${androidResDir}/base", "${androidResDir}/v33"]
            } else {
                srcDirs = ["${androidResDir}/base"]
            }
        }
    }
}

repositories {
    maven {
        // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm

        // First look for the standard location of react-native, as in RN Hello World template
        // https://github.com/facebook/react-native/blob/1e8f3b11027fe0a7514b4fc97d0798d3c64bc895/local-cli/templates/HelloWorld/android/build.gradle#L21
        // TODO(kkafar): Note, that in latest template app https://github.com/react-native-community/template/blob/0f4745b7a9d84232aeedec2def8d75ab9b050d11/template/android/build.gradle
        // this is not specified at all.
        File standardRnAndroidDirLocation = file("$rootDir/../node_modules/react-native/android")
        if (standardRnAndroidDirLocation.exists()) {
            url standardRnAndroidDirLocation
        } else {
            // We're in non standard setup - try to use node resolver to locate the react-native package.
            File reactNativePackage = file(["node", "--print", "require.resolve('react-native/package.json')"].execute(null, rootDir).text.trim())
            def rnAndroidDirLocation = "$reactNativePackage.parentFile/android"
            if (reactNativePackage.exists()) {
                url rnAndroidDirLocation
            } else {
                println "[RNScreens] Failed to resolve react-native directory. Attempted locations: ${standardRnAndroidDirLocation}, ${rnAndroidDirLocation}"
            }
        }
    }

    mavenCentral()
    mavenLocal()
    google()
}

dependencies {
    implementation 'com.facebook.react:react-native:+'
    implementation 'androidx.appcompat:appcompat:1.4.2'
    implementation 'androidx.fragment:fragment:1.3.6'
    implementation 'androidx.coordinatorlayout:coordinatorlayout:1.2.0'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    implementation 'com.google.android.material:material:1.6.1'
    implementation "androidx.core:core-ktx:1.8.0"

    constraints {
        implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1") {
            because("on older React Native versions this dependency conflicts with react-native-screens")
        }
    }
}

{"version": 3, "sources": ["SearchBarNativeComponent.js"], "names": ["codegenNativeComponent", "ColorValue", "tintColor"], "mappings": "AAAA;AACA;AACA;AACA;;AACA;AACA,OAAOA,sBAAP,MAAmC,yDAAnC;AAGA,SAASC,UAAT,QAA2B,8CAA3B;AAKA,SAASC,SAAT,QAA0B,mEAA1B;AA6CA,eAAgBF,sBAAsB,CACpC,cADoC,EAEpC,EAFoC,CAAtC", "sourcesContent": ["/**\n * @flow strict-local\n * @format\n */\n/* eslint-disable */\nimport codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\nimport type { ViewProps } from 'react-native/Libraries/Components/View/ViewPropTypes';\nimport type { HostComponent } from 'react-native/Libraries/Renderer/shims/ReactNativeTypes';\nimport { ColorValue } from 'react-native/Libraries/StyleSheet/StyleSheet';\nimport type {\n  WithDefault,\n  BubblingEventHandler,\n} from 'react-native/Libraries/Types/CodegenTypes';\nimport { tintColor } from 'react-native/Libraries/Components/View/ReactNativeStyleAttributes';\n\ntype SearchBarEvent = $ReadOnly<{||}>;\n\ntype SearchButtonPressedEvent = $ReadOnly<{|\n  text?: string,\n|}>;\n\ntype ChangeTextEvent = $ReadOnly<{|\n  text?: string,\n|}>;\n\ntype AutoCapitalizeType = 'none' | 'words' | 'sentences' | 'characters';\n\ntype NativeProps = $ReadOnly<{|\n  ...ViewProps,\n  onFocus?: ?BubblingEventHandler<SearchBarEvent>,\n  onBlur?: ?BubblingEventHandler<SearchBarEvent>,\n  onSearchButtonPress?: ?BubblingEventHandler<SearchButtonPressedEvent>,\n  onCancelButtonPress?: ?BubblingEventHandler<SearchBarEvent>,\n  onChangeText?: ?BubblingEventHandler<ChangeTextEvent>,\n  hideWhenScrolling?: boolean,\n  autoCapitalize?: WithDefault<AutoCapitalizeType, 'none'>,\n  placeholder?: string,\n  obscureBackground?: boolean,\n  hideNavigationBar?: boolean,\n  cancelButtonText?: string,\n  // TODO: implement these on iOS\n  barTintColor?: ColorValue,\n  tintColor?: ColorValue,\n  textColor?: ColorValue,\n\n  // Android only\n  disableBackButtonOverride?: boolean,\n  // TODO: consider creating enum here\n  inputType?: string,\n  onClose?: ?BubblingEventHandler<SearchBarEvent>,\n  onOpen?: ?BubblingEventHandler<SearchBarEvent>,\n  hintTextColor?: ColorValue,\n  headerIconColor?: ColorValue,\n  shouldShowHintSearchIcon?: WithDefault<boolean, true>,\n|}>;\n\ntype ComponentType = HostComponent<NativeProps>;\n\nexport default (codegenNativeComponent<NativeProps>(\n  'RNSSearchBar',\n  {}\n): ComponentType);\n"]}
{"version": 3, "sources": ["FullWindowOverlayNativeComponent.js"], "names": ["codegenNativeComponent"], "mappings": "AAAA;AACA;AACA;AACA;;AACA;AACA,OAAOA,sBAAP,MAAmC,yDAAnC;AAUA,eAAgBA,sBAAsB,CACpC,sBADoC,EAEpC,EAFoC,CAAtC", "sourcesContent": ["/**\n * @flow strict-local\n * @format\n */\n/* eslint-disable */\nimport codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\nimport type { ViewProps } from 'react-native/Libraries/Components/View/ViewPropTypes';\nimport type { HostComponent } from 'react-native/Libraries/Renderer/shims/ReactNativeTypes';\n\ntype NativeProps = $ReadOnly<{|\n  ...ViewProps,\n|}>;\n\ntype ComponentType = HostComponent<NativeProps>;\n\nexport default (codegenNativeComponent<NativeProps>(\n  'RNSFullWindowOverlay',\n  {}\n): ComponentType);\n"]}
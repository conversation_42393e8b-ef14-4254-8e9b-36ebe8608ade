{"version": 3, "sources": ["ScreenStackNativeComponent.js"], "names": ["codegenNativeComponent"], "mappings": "AAAA;AACA;AACA;AACA;;AACA;AACA,OAAOA,sBAAP,MAAmC,yDAAnC;AAcA,eAAgBA,sBAAsB,CACpC,gBADoC,EAEpC,EAFoC,CAAtC", "sourcesContent": ["/**\n * @flow strict-local\n * @format\n */\n/* eslint-disable */\nimport codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\nimport type { ViewProps } from 'react-native/Libraries/Components/View/ViewPropTypes';\nimport type { HostComponent } from 'react-native/Libraries/Renderer/shims/ReactNativeTypes';\nimport type { DirectEventHandler } from 'react-native/Libraries/Types/CodegenTypes';\n\ntype FinishTransitioningEvent = $ReadOnly<{||}>;\n\ntype NativeProps = $ReadOnly<{|\n  ...ViewProps,\n  onFinishTransitioning?: ?DirectEventHandler<FinishTransitioningEvent>,\n|}>;\n\ntype ComponentType = HostComponent<NativeProps>;\n\nexport default (codegenNativeComponent<NativeProps>(\n  'RNSScreenStack',\n  {}\n): ComponentType);\n"]}
{"version": 3, "sources": ["ScreenStackHeaderConfigNativeComponent.js"], "names": ["React", "codegenNativeComponent"], "mappings": "AAAA;AACA;AACA;AACA;;AACA;AACA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AAEA,OAAOC,sBAAP,MAAmC,yDAAnC;AA2CA,eAAgBA,sBAAsB,CACpC,4BADoC,EAEpC,EAFoC,CAAtC", "sourcesContent": ["/**\n * @flow strict-local\n * @format\n */\n/* eslint-disable */\nimport * as React from 'react';\n\nimport codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\nimport type { ViewProps } from 'react-native/Libraries/Components/View/ViewPropTypes';\nimport type { HostComponent } from 'react-native/Libraries/Renderer/shims/ReactNativeTypes';\nimport type { ColorValue } from 'react-native/Libraries/StyleSheet/StyleSheet';\nimport type {\n  Int32,\n  WithDefault,\n} from 'react-native/Libraries/Types/CodegenTypes';\n\ntype DirectionType = 'rtl' | 'ltr';\n\nexport type NativeProps = $ReadOnly<{|\n  ...ViewProps,\n  backgroundColor?: ColorValue,\n  backTitle?: string,\n  backTitleFontFamily?: string,\n  backTitleFontSize?: Int32,\n  color?: ColorValue,\n  direction?: WithDefault<DirectionType, 'ltr'>,\n  hidden?: boolean,\n  hideShadow?: boolean,\n  largeTitle?: boolean,\n  largeTitleFontFamily?: string,\n  largeTitleFontSize?: Int32,\n  largeTitleFontWeight?: string,\n  largeTitleBackgroundColor?: ColorValue,\n  largeTitleHideShadow?: boolean,\n  largeTitleColor?: ColorValue,\n  translucent?: boolean,\n  title?: string,\n  titleFontFamily?: string,\n  titleFontSize?: Int32,\n  titleFontWeight?: string,\n  titleColor?: ColorValue,\n  disableBackButtonMenu?: boolean,\n  hideBackButton?: boolean,\n  backButtonInCustomView?: boolean,\n  // TODO: implement this props on iOS\n  topInsetEnabled?: boolean,\n|}>;\n\ntype ComponentType = HostComponent<NativeProps>;\n\nexport default (codegenNativeComponent<NativeProps>(\n  'RNSScreenStackHeaderConfig',\n  {}\n): ComponentType);\n"]}
{"version": 3, "sources": ["useReanimatedTransitionProgress.tsx"], "names": ["React", "ReanimatedTransitionProgressContext", "useReanimatedTransitionProgress", "progress", "useContext", "undefined", "Error"], "mappings": "AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AACA,OAAOC,mCAAP,MAAgD,uCAAhD;AAEA,eAAe,SAASC,+BAAT,GAA2C;AACxD,QAAMC,QAAQ,GAAGH,KAAK,CAACI,UAAN,CAAiBH,mCAAjB,CAAjB;;AAEA,MAAIE,QAAQ,KAAKE,SAAjB,EAA4B;AAC1B,UAAM,IAAIC,KAAJ,CACJ,mGADI,CAAN;AAGD;;AAED,SAAOH,QAAP;AACD", "sourcesContent": ["import * as React from 'react';\nimport ReanimatedTransitionProgressContext from './ReanimatedTransitionProgressContext';\n\nexport default function useReanimatedTransitionProgress() {\n  const progress = React.useContext(ReanimatedTransitionProgressContext);\n\n  if (progress === undefined) {\n    throw new Error(\n      \"Couldn't find values for reanimated transition progress. Are you inside a screen in Native Stack?\"\n    );\n  }\n\n  return progress;\n}\n"]}
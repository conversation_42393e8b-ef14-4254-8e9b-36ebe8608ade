{"version": 3, "sources": ["ReanimatedScreen.tsx"], "names": ["React", "InnerScreen", "Animated", "AnimatedScreen", "createAnimatedComponent", "ReanimatedScreen", "forwardRef", "props", "ref", "displayName"], "mappings": ";;AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,SAASC,WAAT,QAAyC,sBAAzC,C,CAEA;;AACA,OAAOC,QAAP,MAAqB,yBAArB;AAEA,MAAMC,cAAc,GAAGD,QAAQ,CAACE,uBAAT,CACpBH,WADoB,CAAvB;AAIA,MAAMI,gBAAgB,gBAAGL,KAAK,CAACM,UAAN,CACvB,CAACC,KAAD,EAAQC,GAAR,KAAgB;AACd,sBACE,oBAAC,cAAD,CACE;AADF;AAEE,IAAA,GAAG,EAAEA;AAFP,KAGMD,KAHN,EADF;AAOD,CATsB,CAAzB;AAYAF,gBAAgB,CAACI,WAAjB,GAA+B,kBAA/B;AAEA,eAAeJ,gBAAf", "sourcesContent": ["import React from 'react';\nimport { InnerScreen, ScreenProps } from 'react-native-screens';\n\n// @ts-ignore file to be used only if `react-native-reanimated` available in the project\nimport Animated from 'react-native-reanimated';\n\nconst AnimatedScreen = Animated.createAnimatedComponent(\n  (InnerScreen as unknown) as React.ComponentClass\n);\n\nconst ReanimatedScreen = React.forwardRef<typeof AnimatedScreen, ScreenProps>(\n  (props, ref) => {\n    return (\n      <AnimatedScreen\n        // @ts-ignore some problems with ref and onTransitionProgressReanimated being \"fake\" prop for parsing of `useEvent` return value\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\n\nReanimatedScreen.displayName = 'ReanimatedScreen';\n\nexport default ReanimatedScreen;\n"]}
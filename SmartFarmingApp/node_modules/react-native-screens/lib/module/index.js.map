{"version": 3, "sources": ["index.tsx"], "names": ["React", "Animated", "View", "Image", "default", "useTransitionProgress", "isSearchBarAvailableForCurrentPlatform", "executeNativeBackPress", "ENABLE_SCREENS", "enableScreens", "shouldEnableScreens", "screensEnabled", "enableFreeze", "shouldEnableReactFreeze", "NativeScreen", "Component", "render", "active", "activityState", "style", "enabled", "rest", "props", "undefined", "display", "Screen", "createAnimatedComponent", "InnerScreen", "ScreenContext", "createContext", "ScreenContainer", "NativeScreenContainer", "NativeScreenNavigationContainer", "ScreenStack", "FullWindowOverlay", "ScreenStackHeaderBackButtonImage", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderSearchBarView", "ScreenStackHeaderConfig", "SearchBar", "ScreenStackHeaderSubview", "shouldUseActivityState"], "mappings": ";;AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,SAASC,QAAT,EAAmBC,IAAnB,EAAgDC,KAAhD,QAA6D,cAA7D;AAUA,cAAc,SAAd;AACA,SAASC,OAAO,IAAIC,qBAApB,QAAiD,yBAAjD;AACA,SACEC,sCADF,EAEEC,sBAFF,QAGO,SAHP;AAKA,IAAIC,cAAc,GAAG,IAArB;AAEA,OAAO,SAASC,aAAT,CAAuBC,mBAAmB,GAAG,IAA7C,EAAyD;AAC9DF,EAAAA,cAAc,GAAGE,mBAAjB;AACD;AAED,OAAO,SAASC,cAAT,GAAmC;AACxC,SAAOH,cAAP;AACD,C,CAED;AACA;;AACA,OAAO,SAASI,YAAT,CAAsBC,uBAAuB,GAAG,IAAhD,EAA4D,CACjE;AACD;AAED,OAAO,MAAMC,YAAN,SAA2Bd,KAAK,CAACe,SAAjC,CAAwD;AAC7DC,EAAAA,MAAM,GAAgB;AACpB,QAAI;AACFC,MAAAA,MADE;AAEFC,MAAAA,aAFE;AAGFC,MAAAA,KAHE;AAIFC,MAAAA,OAAO,GAAGZ,cAJR;AAKF,SAAGa;AALD,QAMA,KAAKC,KANT;;AAQA,QAAIF,OAAJ,EAAa;AACX,UAAIH,MAAM,KAAKM,SAAX,IAAwBL,aAAa,KAAKK,SAA9C,EAAyD;AACvDL,QAAAA,aAAa,GAAGD,MAAM,KAAK,CAAX,GAAe,CAAf,GAAmB,CAAnC,CADuD,CACjB;AACvC;;AACD,0BACE,oBAAC,IAAD,CACE;AADF;AAEE,QAAA,MAAM,EAAEC,aAAa,KAAK,CAF5B;AAGE,QAAA,KAAK,EAAE,CAACC,KAAD,EAAQ;AAAEK,UAAAA,OAAO,EAAEN,aAAa,KAAK,CAAlB,GAAsB,MAAtB,GAA+B;AAA1C,SAAR;AAHT,SAIMG,IAJN,EADF;AAQD;;AAED,wBAAO,oBAAC,IAAD,EAAUA,IAAV,CAAP;AACD;;AAzB4D;AA4B/D,OAAO,MAAMI,MAAM,GAAGxB,QAAQ,CAACyB,uBAAT,CAAiCZ,YAAjC,CAAf;AAEP,OAAO,MAAMa,WAAW,GAAGzB,IAApB;AAEP,OAAO,MAAM0B,aAAa,gBAAG5B,KAAK,CAAC6B,aAAN,CAAoBJ,MAApB,CAAtB;AAEP,OAAO,MAAMK,eAA0D,GAAG5B,IAAnE;AAEP,OAAO,MAAM6B,qBAAgE,GAAG7B,IAAzE;AAEP,OAAO,MAAM8B,+BAA0E,GAAG9B,IAAnF;AAEP,OAAO,MAAM+B,WAAkD,GAAG/B,IAA3D;AAEP,OAAO,MAAMgC,iBAAiB,GAAGhC,IAA1B;AAEP,OAAO,MAAMiC,gCAAgC,GAC3Cb,KAD8C,iBAG9C,oBAAC,IAAD,qBACE,oBAAC,KAAD;AAAO,EAAA,UAAU,EAAC,QAAlB;AAA2B,EAAA,YAAY,EAAE;AAAzC,GAAgDA,KAAhD,EADF,CAHK;AAQP,OAAO,MAAMc,0BAA0B,GACrCd,KADwC,iBAExB,oBAAC,IAAD,EAAUA,KAAV,CAFX;AAIP,OAAO,MAAMe,yBAAyB,GACpCf,KADuC,iBAEvB,oBAAC,IAAD,EAAUA,KAAV,CAFX;AAIP,OAAO,MAAMgB,2BAA2B,GACtChB,KADyC,iBAEzB,oBAAC,IAAD,EAAUA,KAAV,CAFX;AAIP,OAAO,MAAMiB,8BAA8B,GACzCjB,KAD4C,iBAE5B,oBAAC,IAAD,EAAUA,KAAV,CAFX;AAIP,OAAO,MAAMkB,uBAA0E,GAAGtC,IAAnF,C,CAEP;;AACA,OAAO,MAAMuC,SAA8C,GAAGvC,IAAvD;AAEP,OAAO,MAAMwC,wBAEX,GAAGxC,IAFE;AAIP,OAAO,MAAMyC,sBAAsB,GAAG,IAA/B", "sourcesContent": ["import React from 'react';\nimport { Animated, View, ViewProps, ImageProps, Image } from 'react-native';\nimport {\n  ScreenProps,\n  ScreenContainerProps,\n  ScreenStackProps,\n  ScreenStackHeaderConfigProps,\n  HeaderSubviewTypes,\n  SearchBarProps,\n} from './types';\n\nexport * from './types';\nexport { default as useTransitionProgress } from './useTransitionProgress';\nexport {\n  isSearchBarAvailableForCurrentPlatform,\n  executeNativeBackPress,\n} from './utils';\n\nlet ENABLE_SCREENS = true;\n\nexport function enableScreens(shouldEnableScreens = true): void {\n  ENABLE_SCREENS = shouldEnableScreens;\n}\n\nexport function screensEnabled(): boolean {\n  return ENABLE_SCREENS;\n}\n\n// @ts-ignore function stub, freezing logic is located in index.native.tsx\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function enableFreeze(shouldEnableReactFreeze = true): void {\n  // noop\n}\n\nexport class NativeScreen extends React.Component<ScreenProps> {\n  render(): JSX.Element {\n    let {\n      active,\n      activityState,\n      style,\n      enabled = ENABLE_SCREENS,\n      ...rest\n    } = this.props;\n\n    if (enabled) {\n      if (active !== undefined && activityState === undefined) {\n        activityState = active !== 0 ? 2 : 0; // change taken from index.native.tsx\n      }\n      return (\n        <View\n          // @ts-expect-error: hidden exists on web, but not in React Native\n          hidden={activityState === 0}\n          style={[style, { display: activityState !== 0 ? 'flex' : 'none' }]}\n          {...rest}\n        />\n      );\n    }\n\n    return <View {...rest} />;\n  }\n}\n\nexport const Screen = Animated.createAnimatedComponent(NativeScreen);\n\nexport const InnerScreen = View;\n\nexport const ScreenContext = React.createContext(Screen);\n\nexport const ScreenContainer: React.ComponentType<ScreenContainerProps> = View;\n\nexport const NativeScreenContainer: React.ComponentType<ScreenContainerProps> = View;\n\nexport const NativeScreenNavigationContainer: React.ComponentType<ScreenContainerProps> = View;\n\nexport const ScreenStack: React.ComponentType<ScreenStackProps> = View;\n\nexport const FullWindowOverlay = View;\n\nexport const ScreenStackHeaderBackButtonImage = (\n  props: ImageProps\n): JSX.Element => (\n  <View>\n    <Image resizeMode=\"center\" fadeDuration={0} {...props} />\n  </View>\n);\n\nexport const ScreenStackHeaderRightView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderLeftView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderCenterView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderSearchBarView = (\n  props: React.PropsWithChildren<SearchBarProps>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderConfig: React.ComponentType<ScreenStackHeaderConfigProps> = View;\n\n// @ts-expect-error: search bar props have no common props with View\nexport const SearchBar: React.ComponentType<SearchBarProps> = View;\n\nexport const ScreenStackHeaderSubview: React.ComponentType<React.PropsWithChildren<\n  ViewProps & { type?: HeaderSubviewTypes }\n>> = View;\n\nexport const shouldUseActivityState = true;\n"]}
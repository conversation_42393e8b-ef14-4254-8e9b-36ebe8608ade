{"version": 3, "sources": ["useHeaderHeight.tsx"], "names": ["React", "HeaderHeightContext", "useHeaderHeight", "height", "useContext", "undefined", "Error"], "mappings": "AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AAEA,OAAOC,mBAAP,MAAgC,uBAAhC;AAEA,eAAe,SAASC,eAAT,GAA2B;AACxC,QAAMC,MAAM,GAAGH,KAAK,CAACI,UAAN,CAAiBH,mBAAjB,CAAf;;AAEA,MAAIE,MAAM,KAAKE,SAAf,EAA0B;AACxB,UAAM,IAAIC,KAAJ,CACJ,wFADI,CAAN;AAGD;;AAED,SAAOH,MAAP;AACD", "sourcesContent": ["import * as React from 'react';\n\nimport HeaderHeightContext from './HeaderHeightContext';\n\nexport default function useHeaderHeight() {\n  const height = React.useContext(HeaderHeightContext);\n\n  if (height === undefined) {\n    throw new Error(\n      \"Couldn't find the header height. Are you inside a screen in a navigator with a header?\"\n    );\n  }\n\n  return height;\n}\n"]}
{"version": 3, "sources": ["getDefaultHeaderHeight.tsx"], "names": ["Platform", "formSheetModalHeight", "getDefaultHeaderHeight", "layout", "topInset", "stackPresentation", "headerHeight", "OS", "statusBarHeight", "isLandscape", "width", "height", "isFromSheetModal", "isPad", "isTV"], "mappings": "AAAA,SAASA,QAAT,QAAyB,cAAzB;AAIA,MAAMC,oBAAoB,GAAG,EAA7B;AAEA,eAAe,SAASC,sBAAT,CACbC,MADa,EAEbC,QAFa,EAGbC,iBAHa,EAIL;AACR;AACA,MAAIC,YAAY,GAAGN,QAAQ,CAACO,EAAT,KAAgB,SAAhB,GAA4B,EAA5B,GAAiC,EAApD;AACA,MAAIC,eAAe,GAAGJ,QAAtB;;AAEA,MAAIJ,QAAQ,CAACO,EAAT,KAAgB,KAApB,EAA2B;AACzB,UAAME,WAAW,GAAGN,MAAM,CAACO,KAAP,GAAeP,MAAM,CAACQ,MAA1C;AACA,UAAMC,gBAAgB,GACpBP,iBAAiB,KAAK,OAAtB,IAAiCA,iBAAiB,KAAK,WADzD;;AAEA,QAAIO,gBAAgB,IAAI,CAACH,WAAzB,EAAsC;AACpC;AACAD,MAAAA,eAAe,GAAG,CAAlB;AACD;;AAED,QAAIR,QAAQ,CAACa,KAAT,IAAkBb,QAAQ,CAACc,IAA/B,EAAqC;AACnCR,MAAAA,YAAY,GAAGM,gBAAgB,GAAGX,oBAAH,GAA0B,EAAzD;AACD,KAFD,MAEO;AACL,UAAIQ,WAAJ,EAAiB;AACfH,QAAAA,YAAY,GAAG,EAAf;AACD,OAFD,MAEO;AACLA,QAAAA,YAAY,GAAGM,gBAAgB,GAAGX,oBAAH,GAA0B,EAAzD;AACD;AACF;AACF;;AAED,SAAOK,YAAY,GAAGE,eAAtB;AACD", "sourcesContent": ["import { Platform } from 'react-native';\nimport { StackPresentationTypes } from 'react-native-screens';\ntype Layout = { width: number; height: number };\n\nconst formSheetModalHeight = 56;\n\nexport default function getDefaultHeaderHeight(\n  layout: Layout,\n  topInset: number,\n  stackPresentation: StackPresentationTypes\n): number {\n  // default header heights\n  let headerHeight = Platform.OS === 'android' ? 56 : 64;\n  let statusBarHeight = topInset;\n\n  if (Platform.OS === 'ios') {\n    const isLandscape = layout.width > layout.height;\n    const isFromSheetModal =\n      stackPresentation === 'modal' || stackPresentation === 'formSheet';\n    if (isFromSheetModal && !isLandscape) {\n      // `modal` and `formSheet` presentations do not take whole screen, so should not take the inset.\n      statusBarHeight = 0;\n    }\n\n    if (Platform.isPad || Platform.isTV) {\n      headerHeight = isFromSheetModal ? formSheetModalHeight : 50;\n    } else {\n      if (isLandscape) {\n        headerHeight = 32;\n      } else {\n        headerHeight = isFromSheetModal ? formSheetModalHeight : 44;\n      }\n    }\n  }\n\n  return headerHeight + statusBarHeight;\n}\n"]}
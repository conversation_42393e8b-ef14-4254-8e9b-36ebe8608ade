{"version": 3, "sources": ["useBackPressSubscription.tsx"], "names": ["React", "<PERSON><PERSON><PERSON><PERSON>", "useBackPressSubscription", "onBackPress", "isDisabled", "isActive", "setIsActive", "useState", "subscription", "useRef", "clearSubscription", "useCallback", "shouldSetActive", "current", "remove", "undefined", "createSubscription", "addEventListener", "handleAttached", "handleDetached", "useEffect"], "mappings": "AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,SAASC,WAAT,QAAqD,cAArD;;AAcA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAT,CAAkC;AACvCC,EAAAA,WADuC;AAEvCC,EAAAA;AAFuC,CAAlC,EAG4B;AACjC,QAAM,CAACC,QAAD,EAAWC,WAAX,IAA0BN,KAAK,CAACO,QAAN,CAAe,KAAf,CAAhC;AACA,QAAMC,YAAY,GAAGR,KAAK,CAACS,MAAN,EAArB;AAEA,QAAMC,iBAAiB,GAAGV,KAAK,CAACW,WAAN,CAAkB,CAACC,eAAe,GAAG,IAAnB,KAA4B;AAAA;;AACtE,6BAAAJ,YAAY,CAACK,OAAb,gFAAsBC,MAAtB;AACAN,IAAAA,YAAY,CAACK,OAAb,GAAuBE,SAAvB;AACA,QAAIH,eAAJ,EAAqBN,WAAW,CAAC,KAAD,CAAX;AACtB,GAJyB,EAIvB,EAJuB,CAA1B;AAMA,QAAMU,kBAAkB,GAAGhB,KAAK,CAACW,WAAN,CAAkB,MAAM;AACjD,QAAI,CAACP,UAAL,EAAiB;AAAA;;AACf,gCAAAI,YAAY,CAACK,OAAb,kFAAsBC,MAAtB;AACAN,MAAAA,YAAY,CAACK,OAAb,GAAuBZ,WAAW,CAACgB,gBAAZ,CACrB,mBADqB,EAErBd,WAFqB,CAAvB;AAIAG,MAAAA,WAAW,CAAC,IAAD,CAAX;AACD;AACF,GAT0B,EASxB,CAACF,UAAD,EAAaD,WAAb,CATwB,CAA3B;AAWA,QAAMe,cAAc,GAAGlB,KAAK,CAACW,WAAN,CAAkB,MAAM;AAC7C,QAAIN,QAAJ,EAAc;AACZW,MAAAA,kBAAkB;AACnB;AACF,GAJsB,EAIpB,CAACA,kBAAD,EAAqBX,QAArB,CAJoB,CAAvB;AAMA,QAAMc,cAAc,GAAGnB,KAAK,CAACW,WAAN,CAAkB,MAAM;AAC7CD,IAAAA,iBAAiB,CAAC,KAAD,CAAjB;AACD,GAFsB,EAEpB,CAACA,iBAAD,CAFoB,CAAvB;AAIAV,EAAAA,KAAK,CAACoB,SAAN,CAAgB,MAAM;AACpB,QAAIhB,UAAJ,EAAgB;AACdM,MAAAA,iBAAiB;AAClB;AACF,GAJD,EAIG,CAACN,UAAD,EAAaM,iBAAb,CAJH;AAMA,SAAO;AACLQ,IAAAA,cADK;AAELC,IAAAA,cAFK;AAGLH,IAAAA,kBAHK;AAILN,IAAAA;AAJK,GAAP;AAMD", "sourcesContent": ["import React from 'react';\nimport { BackHandler, NativeEventSubscription } from 'react-native';\n\ninterface Args {\n  onBackPress: () => boolean;\n  isDisabled: boolean;\n}\n\ninterface UseBackPressSubscription {\n  handleAttached: () => void;\n  handleDetached: () => void;\n  createSubscription: () => void;\n  clearSubscription: () => void;\n}\n\n/**\n * This hook is an abstraction for keeping back press subscription\n * logic in one place.\n */\nexport function useBackPressSubscription({\n  onBackPress,\n  isDisabled,\n}: Args): UseBackPressSubscription {\n  const [isActive, setIsActive] = React.useState(false);\n  const subscription = React.useRef<NativeEventSubscription | undefined>();\n\n  const clearSubscription = React.useCallback((shouldSetActive = true) => {\n    subscription.current?.remove();\n    subscription.current = undefined;\n    if (shouldSetActive) setIsActive(false);\n  }, []);\n\n  const createSubscription = React.useCallback(() => {\n    if (!isDisabled) {\n      subscription.current?.remove();\n      subscription.current = BackHandler.addEventListener(\n        'hardwareBackPress',\n        onBackPress\n      );\n      setIsActive(true);\n    }\n  }, [isDisabled, onBackPress]);\n\n  const handleAttached = React.useCallback(() => {\n    if (isActive) {\n      createSubscription();\n    }\n  }, [createSubscription, isActive]);\n\n  const handleDetached = React.useCallback(() => {\n    clearSubscription(false);\n  }, [clearSubscription]);\n\n  React.useEffect(() => {\n    if (isDisabled) {\n      clearSubscription();\n    }\n  }, [isDisabled, clearSubscription]);\n\n  return {\n    handleAttached,\n    handleDetached,\n    createSubscription,\n    clearSubscription,\n  };\n}\n"]}
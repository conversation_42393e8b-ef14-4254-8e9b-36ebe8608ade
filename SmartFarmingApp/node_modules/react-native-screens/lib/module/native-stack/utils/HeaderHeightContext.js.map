{"version": 3, "sources": ["HeaderHeightContext.tsx"], "names": ["React", "HeaderHeightContext", "createContext", "undefined"], "mappings": "AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AAEA,MAAMC,mBAAmB,gBAAGD,KAAK,CAACE,aAAN,CAAwCC,SAAxC,CAA5B;AAEA,eAAeF,mBAAf", "sourcesContent": ["import * as React from 'react';\n\nconst HeaderHeightContext = React.createContext<number | undefined>(undefined);\n\nexport default HeaderHeightContext;\n"]}
{"version": 3, "sources": ["createNativeStackNavigator.tsx"], "names": ["createNavigatorFactory", "StackActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useNavigationBuilder", "React", "NativeStackView", "NativeStackNavigator", "initialRouteName", "children", "screenOptions", "rest", "state", "descriptors", "navigation", "useEffect", "dangerouslyGetParent", "undefined", "console", "warn", "addListener", "e", "isFocused", "requestAnimationFrame", "index", "defaultPrevented", "dispatch", "popToTop", "target", "key"], "mappings": ";;AAAA,SACEA,sBADF,EAGEC,YAHF,EAMEC,WANF,EASEC,oBATF,QAUO,0BAVP;AAWA,OAAO,KAAKC,KAAZ,MAAuB,OAAvB;AAMA,OAAOC,eAAP,MAA4B,0BAA5B;;AAEA,SAASC,oBAAT,CAA8B;AAC5BC,EAAAA,gBAD4B;AAE5BC,EAAAA,QAF4B;AAG5BC,EAAAA,aAH4B;AAI5B,KAAGC;AAJyB,CAA9B,EAK8B;AAC5B,QAAM;AAAEC,IAAAA,KAAF;AAASC,IAAAA,WAAT;AAAsBC,IAAAA;AAAtB,MAAqCV,oBAAoB,CAM7DD,WAN6D,EAMhD;AACbK,IAAAA,gBADa;AAEbC,IAAAA,QAFa;AAGbC,IAAAA;AAHa,GANgD,CAA/D,CAD4B,CAa5B;AACA;;AACAL,EAAAA,KAAK,CAACU,SAAN,CAAgB,MAAM;AACpB;AACA,QAAI,CAAAD,UAAU,SAAV,IAAAA,UAAU,WAAV,YAAAA,UAAU,CAAEE,oBAAZ,MAAqCC,SAAzC,EAAoD;AAClDC,MAAAA,OAAO,CAACC,IAAR,CACE,2LADF;AAGD;AACF,GAPD,EAOG,CAACL,UAAD,CAPH;AASAT,EAAAA,KAAK,CAACU,SAAN,CACE;AAAA;;AAAA,WACED,UADF,aACEA,UADF,gDACEA,UAAU,CAAEM,WADd,0DACE,2BAAAN,UAAU,EAAgB,UAAhB,EAA6BO,CAAD,IAAO;AAC3C,YAAMC,SAAS,GAAGR,UAAU,CAACQ,SAAX,EAAlB,CAD2C,CAG3C;AACA;;AACAC,MAAAA,qBAAqB,CAAC,MAAM;AAC1B,YACEX,KAAK,CAACY,KAAN,GAAc,CAAd,IACAF,SADA,IAEA,CAAED,CAAD,CAAkCI,gBAHrC,EAIE;AACA;AACA;AACAX,UAAAA,UAAU,CAACY,QAAX,CAAoB,EAClB,GAAGxB,YAAY,CAACyB,QAAb,EADe;AAElBC,YAAAA,MAAM,EAAEhB,KAAK,CAACiB;AAFI,WAApB;AAID;AACF,OAboB,CAArB;AAcD,KAnBS,CADZ;AAAA,GADF,EAsBE,CAACf,UAAD,EAAaF,KAAK,CAACY,KAAnB,EAA0BZ,KAAK,CAACiB,GAAhC,CAtBF;AAyBA,sBACE,oBAAC,eAAD,eACMlB,IADN;AAEE,IAAA,KAAK,EAAEC,KAFT;AAGE,IAAA,UAAU,EAAEE,UAHd;AAIE,IAAA,WAAW,EAAED;AAJf,KADF;AAQD;;AAED,eAAeZ,sBAAsB,CAKnCM,oBALmC,CAArC", "sourcesContent": ["import {\n  createNavigator<PERSON><PERSON>y,\n  EventArg,\n  StackActions,\n  StackActionHelpers,\n  StackNavigationState,\n  StackRouter,\n  StackRouterOptions,\n  ParamListBase,\n  useNavigationBuilder,\n} from '@react-navigation/native';\nimport * as React from 'react';\nimport {\n  NativeStackNavigationEventMap,\n  NativeStackNavigationOptions,\n  NativeStackNavigatorProps,\n} from '../types';\nimport NativeStackView from '../views/NativeStackView';\n\nfunction NativeStackNavigator({\n  initialRouteName,\n  children,\n  screenOptions,\n  ...rest\n}: NativeStackNavigatorProps) {\n  const { state, descriptors, navigation } = useNavigationBuilder<\n    StackNavigationState<ParamListBase>,\n    StackRouterOptions,\n    StackActionHelpers<ParamListBase>,\n    NativeStackNavigationOptions,\n    NativeStackNavigationEventMap\n  >(StackRouter, {\n    initialRouteName,\n    children,\n    screenOptions,\n  });\n\n  // Starting from React Navigation v6, `native-stack` should be imported from\n  // `@react-navigation/native-stack` rather than `react-native-screens/native-stack`\n  React.useEffect(() => {\n    // @ts-ignore navigation.dangerouslyGetParent was removed in v6\n    if (navigation?.dangerouslyGetParent === undefined) {\n      console.warn(\n        'Looks like you are importing `native-stack` from `react-native-screens/native-stack`. Since version 6 of `react-navigation`, it should be imported from `@react-navigation/native-stack`.'\n      );\n    }\n  }, [navigation]);\n\n  React.useEffect(\n    () =>\n      navigation?.addListener?.('tabPress', (e) => {\n        const isFocused = navigation.isFocused();\n\n        // Run the operation in the next frame so we're sure all listeners have been run\n        // This is necessary to know if preventDefault() has been called\n        requestAnimationFrame(() => {\n          if (\n            state.index > 0 &&\n            isFocused &&\n            !(e as EventArg<'tabPress', true>).defaultPrevented\n          ) {\n            // When user taps on already focused tab and we're inside the tab,\n            // reset the stack to replicate native behaviour\n            navigation.dispatch({\n              ...StackActions.popToTop(),\n              target: state.key,\n            });\n          }\n        });\n      }),\n    [navigation, state.index, state.key]\n  );\n\n  return (\n    <NativeStackView\n      {...rest}\n      state={state}\n      navigation={navigation}\n      descriptors={descriptors}\n    />\n  );\n}\n\nexport default createNavigatorFactory<\n  StackNavigationState<ParamListBase>,\n  NativeStackNavigationOptions,\n  NativeStackNavigationEventMap,\n  typeof NativeStackNavigator\n>(NativeStackNavigator);\n"]}
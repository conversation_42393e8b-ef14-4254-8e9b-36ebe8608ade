{"version": 3, "sources": ["index.tsx"], "names": ["default", "createNativeStackNavigator", "NativeStackView", "useHeaderHeight", "HeaderHeightContext"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,OAAO,IAAIC,0BAApB,QAAsD,yCAAtD;AAEA;AACA;AACA;;AACA,SAASD,OAAO,IAAIE,eAApB,QAA2C,yBAA3C;AAEA;AACA;AACA;;AACA,SAASF,OAAO,IAAIG,eAApB,QAA2C,yBAA3C;AACA,SAASH,OAAO,IAAII,mBAApB,QAA+C,6BAA/C;AAEA;AACA;AACA", "sourcesContent": ["/**\n * Navigators\n */\nexport { default as createNativeStackNavigator } from './navigators/createNativeStackNavigator';\n\n/**\n * Views\n */\nexport { default as NativeStackView } from './views/NativeStackView';\n\n/**\n * Utilities\n */\nexport { default as useHeaderHeight } from './utils/useHeaderHeight';\nexport { default as HeaderHeightContext } from './utils/HeaderHeightContext';\n\n/**\n * Types\n */\nexport type {\n  NativeStackNavigationOptions,\n  NativeStackNavigationProp,\n  NativeStackScreenProps,\n} from './types';\n"]}
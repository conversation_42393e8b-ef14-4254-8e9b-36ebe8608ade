{"version": 3, "sources": ["HeaderConfig.tsx"], "names": ["useTheme", "React", "Platform", "ScreenStackHeaderBackButtonImage", "ScreenStackHeaderCenterView", "ScreenStackHeaderConfig", "ScreenStackHeaderLeftView", "ScreenStackHeaderRightView", "ScreenStackHeaderSearchBarView", "SearchBar", "isSearchBarAvailableForCurrentPlatform", "executeNativeBackPress", "useBackPressSubscription", "processFonts", "HeaderConfig", "backButtonImage", "backButtonInCustomView", "direction", "disableBackButtonMenu", "headerBackTitle", "headerBackTitleStyle", "headerBackTitleVisible", "headerCenter", "headerHideBackButton", "headerHideShadow", "headerLargeStyle", "headerLargeTitle", "headerLargeTitleHideShadow", "headerLargeTitleStyle", "headerLeft", "headerRight", "headerShown", "headerStyle", "headerTintColor", "headerTitle", "headerTitleStyle", "headerTopInsetEnabled", "headerTranslucent", "route", "searchBar", "title", "colors", "tintColor", "primary", "handleAttached", "handleDetached", "clearSubscription", "createSubscription", "onBackPress", "isDisabled", "disableBackButtonOverride", "backTitleFontFamily", "largeTitleFontFamily", "titleFontFamily", "fontFamily", "useEffect", "processedSearchBarOptions", "useMemo", "OS", "onFocus", "args", "onClose", "backgroundColor", "card", "fontSize", "blurEffect", "color", "fontWeight", "undefined", "name", "text"], "mappings": "AAAA,SAAgBA,QAAhB,QAAgC,0BAAhC;AACA,OAAO,KAAKC,KAAZ,MAAuB,OAAvB;AACA,SAASC,QAAT,QAAyB,cAAzB;AACA,SACEC,gCADF,EAEEC,2BAFF,EAGEC,uBAHF,EAIEC,yBAJF,EAKEC,0BALF,EAMEC,8BANF,EAOEC,SAPF,EASEC,sCATF,EAUEC,sBAVF,QAWO,sBAXP;AAaA,SAASC,wBAAT,QAAyC,mCAAzC;AACA,SAASC,YAAT,QAA6B,iBAA7B;AAMA,eAAe,SAASC,YAAT,CAAsB;AACnCC,EAAAA,eADmC;AAEnCC,EAAAA,sBAFmC;AAGnCC,EAAAA,SAHmC;AAInCC,EAAAA,qBAJmC;AAKnCC,EAAAA,eALmC;AAMnCC,EAAAA,oBAAoB,GAAG,EANY;AAOnCC,EAAAA,sBAAsB,GAAG,IAPU;AAQnCC,EAAAA,YARmC;AASnCC,EAAAA,oBATmC;AAUnCC,EAAAA,gBAVmC;AAWnCC,EAAAA,gBAAgB,GAAG,EAXgB;AAYnCC,EAAAA,gBAZmC;AAanCC,EAAAA,0BAbmC;AAcnCC,EAAAA,qBAAqB,GAAG,EAdW;AAenCC,EAAAA,UAfmC;AAgBnCC,EAAAA,WAhBmC;AAiBnCC,EAAAA,WAjBmC;AAkBnCC,EAAAA,WAAW,GAAG,EAlBqB;AAmBnCC,EAAAA,eAnBmC;AAoBnCC,EAAAA,WApBmC;AAqBnCC,EAAAA,gBAAgB,GAAG,EArBgB;AAsBnCC,EAAAA,qBAAqB,GAAG,IAtBW;AAuBnCC,EAAAA,iBAvBmC;AAwBnCC,EAAAA,KAxBmC;AAyBnCC,EAAAA,SAzBmC;AA0BnCC,EAAAA;AA1BmC,CAAtB,EA2BQ;AACrB,QAAM;AAAEC,IAAAA;AAAF,MAAazC,QAAQ,EAA3B;AACA,QAAM0C,SAAS,GAAGT,eAAH,aAAGA,eAAH,cAAGA,eAAH,GAAsBQ,MAAM,CAACE,OAA5C,CAFqB,CAIrB;AACA;AACA;;AACA,QAAM;AACJC,IAAAA,cADI;AAEJC,IAAAA,cAFI;AAGJC,IAAAA,iBAHI;AAIJC,IAAAA;AAJI,MAKFnC,wBAAwB,CAAC;AAC3BoC,IAAAA,WAAW,EAAErC,sBADc;AAE3BsC,IAAAA,UAAU,EAAE,CAACV,SAAD,IAAc,CAAC,CAACA,SAAS,CAACW;AAFX,GAAD,CAL5B;AAUA,QAAM,CACJC,mBADI,EAEJC,oBAFI,EAGJC,eAHI,IAIFxC,YAAY,CAAC,CACfO,oBAAoB,CAACkC,UADN,EAEf1B,qBAAqB,CAAC0B,UAFP,EAGfnB,gBAAgB,CAACmB,UAHF,CAAD,CAJhB,CAjBqB,CA2BrB;;AACArD,EAAAA,KAAK,CAACsD,SAAN,CAAgB,MAAMT,iBAAtB,EAAyC,CAACP,SAAD,CAAzC;AAEA,QAAMiB,yBAAyB,GAAGvD,KAAK,CAACwD,OAAN,CAAc,MAAM;AACpD,QACEvD,QAAQ,CAACwD,EAAT,KAAgB,SAAhB,IACAnB,SADA,IAEA,CAACA,SAAS,CAACW,yBAHb,EAIE;AACA,YAAMS,OAAkC,GAAG,CAAC,GAAGC,IAAJ,KAAa;AAAA;;AACtDb,QAAAA,kBAAkB;AAClB,8BAAAR,SAAS,CAACoB,OAAV,+EAAApB,SAAS,EAAW,GAAGqB,IAAd,CAAT;AACD,OAHD;;AAIA,YAAMC,OAAkC,GAAG,CAAC,GAAGD,IAAJ,KAAa;AAAA;;AACtDd,QAAAA,iBAAiB;AACjB,8BAAAP,SAAS,CAACsB,OAAV,+EAAAtB,SAAS,EAAW,GAAGqB,IAAd,CAAT;AACD,OAHD;;AAKA,aAAO,EAAE,GAAGrB,SAAL;AAAgBoB,QAAAA,OAAhB;AAAyBE,QAAAA;AAAzB,OAAP;AACD;;AACD,WAAOtB,SAAP;AACD,GAlBiC,EAkB/B,CAACA,SAAD,EAAYQ,kBAAZ,EAAgCD,iBAAhC,CAlB+B,CAAlC;AAoBA,sBACE,oBAAC,uBAAD;AACE,IAAA,sBAAsB,EAAE9B,sBAD1B;AAEE,IAAA,eAAe,EACbgB,WAAW,CAAC8B,eAAZ,GAA8B9B,WAAW,CAAC8B,eAA1C,GAA4DrB,MAAM,CAACsB,IAHvE;AAKE,IAAA,SAAS,EAAE1C,sBAAsB,GAAGF,eAAH,GAAqB,GALxD;AAME,IAAA,mBAAmB,EAAEgC,mBANvB;AAOE,IAAA,iBAAiB,EAAE/B,oBAAoB,CAAC4C,QAP1C;AAQE,IAAA,UAAU,EAAEhC,WAAW,CAACiC,UAR1B;AASE,IAAA,KAAK,EAAEvB,SATT;AAUE,IAAA,SAAS,EAAEzB,SAVb;AAWE,IAAA,qBAAqB,EAAEC,qBAXzB;AAYE,IAAA,MAAM,EAAEa,WAAW,KAAK,KAZ1B;AAaE,IAAA,cAAc,EAAER,oBAblB;AAcE,IAAA,UAAU,EAAEC,gBAdd;AAeE,IAAA,UAAU,EAAEE,gBAfd;AAgBE,IAAA,yBAAyB,EAAED,gBAAgB,CAACqC,eAhB9C;AAiBE,IAAA,eAAe,EAAElC,qBAAqB,CAACsC,KAjBzC;AAkBE,IAAA,oBAAoB,EAAEd,oBAlBxB;AAmBE,IAAA,kBAAkB,EAAExB,qBAAqB,CAACoC,QAnB5C;AAoBE,IAAA,oBAAoB,EAAEpC,qBAAqB,CAACuC,UApB9C;AAqBE,IAAA,oBAAoB,EAAExC,0BArBxB;AAsBE,IAAA,KAAK,EACHO,WAAW,KAAKkC,SAAhB,GACIlC,WADJ,GAEIM,KAAK,KAAK4B,SAAV,GACA5B,KADA,GAEAF,KAAK,CAAC+B,IA3Bd;AA6BE,IAAA,UAAU,EACRlC,gBAAgB,CAAC+B,KAAjB,KAA2BE,SAA3B,GACIjC,gBAAgB,CAAC+B,KADrB,GAEIjC,eAAe,KAAKmC,SAApB,GACAnC,eADA,GAEAQ,MAAM,CAAC6B,IAlCf;AAoCE,IAAA,eAAe,EAAEjB,eApCnB;AAqCE,IAAA,aAAa,EAAElB,gBAAgB,CAAC6B,QArClC;AAsCE,IAAA,eAAe,EAAE7B,gBAAgB,CAACgC,UAtCpC;AAuCE,IAAA,eAAe,EAAE/B,qBAvCnB;AAwCE,IAAA,WAAW,EAAEC,iBAAiB,KAAK,IAxCrC;AAyCE,IAAA,UAAU,EAAEO,cAzCd;AA0CE,IAAA,UAAU,EAAEC;AA1Cd,KA2CGf,WAAW,KAAKsC,SAAhB,gBACC,oBAAC,0BAAD,QACGtC,WAAW,CAAC;AAAEY,IAAAA;AAAF,GAAD,CADd,CADD,GAIG,IA/CN,EAgDG3B,eAAe,KAAKqD,SAApB,gBACC,oBAAC,gCAAD;AACE,IAAA,GAAG,EAAC,WADN;AAEE,IAAA,MAAM,EAAErD;AAFV,IADD,GAKG,IArDN,EAsDGc,UAAU,KAAKuC,SAAf,gBACC,oBAAC,yBAAD,QACGvC,UAAU,CAAC;AAAEa,IAAAA;AAAF,GAAD,CADb,CADD,GAIG,IA1DN,EA2DGpB,YAAY,KAAK8C,SAAjB,gBACC,oBAAC,2BAAD,QACG9C,YAAY,CAAC;AAAEoB,IAAAA;AAAF,GAAD,CADf,CADD,GAIG,IA/DN,EAgEGhC,sCAAsC,IACvC8C,yBAAyB,KAAKY,SAD7B,gBAEC,oBAAC,8BAAD,qBACE,oBAAC,SAAD,EAAeZ,yBAAf,CADF,CAFD,GAKG,IArEN,CADF;AAyED", "sourcesContent": ["import { Route, useTheme } from '@react-navigation/native';\nimport * as React from 'react';\nimport { Platform } from 'react-native';\nimport {\n  ScreenStackHeaderBackButtonImage,\n  ScreenStackHeaderCenterView,\n  ScreenStackHeaderConfig,\n  ScreenStackHeaderLeftView,\n  ScreenStackHeaderRightView,\n  ScreenStackHeaderSearchBarView,\n  SearchBar,\n  SearchBarProps,\n  isSearchBarAvailableForCurrentPlatform,\n  executeNativeBackPress,\n} from 'react-native-screens';\nimport { NativeStackNavigationOptions } from '../types';\nimport { useBackPressSubscription } from '../utils/useBackPressSubscription';\nimport { processFonts } from './FontProcessor';\n\ntype Props = NativeStackNavigationOptions & {\n  route: Route<string>;\n};\n\nexport default function HeaderConfig({\n  backButtonImage,\n  backButtonInCustomView,\n  direction,\n  disableBackButtonMenu,\n  headerBackTitle,\n  headerBackTitleStyle = {},\n  headerBackTitleVisible = true,\n  headerCenter,\n  headerHideBackButton,\n  headerHideShadow,\n  headerLargeStyle = {},\n  headerLargeTitle,\n  headerLargeTitleHideShadow,\n  headerLargeTitleStyle = {},\n  headerLeft,\n  headerRight,\n  headerShown,\n  headerStyle = {},\n  headerTintColor,\n  headerTitle,\n  headerTitleStyle = {},\n  headerTopInsetEnabled = true,\n  headerTranslucent,\n  route,\n  searchBar,\n  title,\n}: Props): JSX.Element {\n  const { colors } = useTheme();\n  const tintColor = headerTintColor ?? colors.primary;\n\n  // We need to use back press subscription here to override back button behavior on JS side.\n  // Because screens are usually used with react-navigation and this library overrides back button\n  // we need to handle it first in case when search bar is open\n  const {\n    handleAttached,\n    handleDetached,\n    clearSubscription,\n    createSubscription,\n  } = useBackPressSubscription({\n    onBackPress: executeNativeBackPress,\n    isDisabled: !searchBar || !!searchBar.disableBackButtonOverride,\n  });\n\n  const [\n    backTitleFontFamily,\n    largeTitleFontFamily,\n    titleFontFamily,\n  ] = processFonts([\n    headerBackTitleStyle.fontFamily,\n    headerLargeTitleStyle.fontFamily,\n    headerTitleStyle.fontFamily,\n  ]);\n\n  // We want to clear clearSubscription only when components unmounts or search bar changes\n  React.useEffect(() => clearSubscription, [searchBar]);\n\n  const processedSearchBarOptions = React.useMemo(() => {\n    if (\n      Platform.OS === 'android' &&\n      searchBar &&\n      !searchBar.disableBackButtonOverride\n    ) {\n      const onFocus: SearchBarProps['onFocus'] = (...args) => {\n        createSubscription();\n        searchBar.onFocus?.(...args);\n      };\n      const onClose: SearchBarProps['onClose'] = (...args) => {\n        clearSubscription();\n        searchBar.onClose?.(...args);\n      };\n\n      return { ...searchBar, onFocus, onClose };\n    }\n    return searchBar;\n  }, [searchBar, createSubscription, clearSubscription]);\n\n  return (\n    <ScreenStackHeaderConfig\n      backButtonInCustomView={backButtonInCustomView}\n      backgroundColor={\n        headerStyle.backgroundColor ? headerStyle.backgroundColor : colors.card\n      }\n      backTitle={headerBackTitleVisible ? headerBackTitle : ' '}\n      backTitleFontFamily={backTitleFontFamily}\n      backTitleFontSize={headerBackTitleStyle.fontSize}\n      blurEffect={headerStyle.blurEffect}\n      color={tintColor}\n      direction={direction}\n      disableBackButtonMenu={disableBackButtonMenu}\n      hidden={headerShown === false}\n      hideBackButton={headerHideBackButton}\n      hideShadow={headerHideShadow}\n      largeTitle={headerLargeTitle}\n      largeTitleBackgroundColor={headerLargeStyle.backgroundColor}\n      largeTitleColor={headerLargeTitleStyle.color}\n      largeTitleFontFamily={largeTitleFontFamily}\n      largeTitleFontSize={headerLargeTitleStyle.fontSize}\n      largeTitleFontWeight={headerLargeTitleStyle.fontWeight}\n      largeTitleHideShadow={headerLargeTitleHideShadow}\n      title={\n        headerTitle !== undefined\n          ? headerTitle\n          : title !== undefined\n          ? title\n          : route.name\n      }\n      titleColor={\n        headerTitleStyle.color !== undefined\n          ? headerTitleStyle.color\n          : headerTintColor !== undefined\n          ? headerTintColor\n          : colors.text\n      }\n      titleFontFamily={titleFontFamily}\n      titleFontSize={headerTitleStyle.fontSize}\n      titleFontWeight={headerTitleStyle.fontWeight}\n      topInsetEnabled={headerTopInsetEnabled}\n      translucent={headerTranslucent === true}\n      onAttached={handleAttached}\n      onDetached={handleDetached}>\n      {headerRight !== undefined ? (\n        <ScreenStackHeaderRightView>\n          {headerRight({ tintColor })}\n        </ScreenStackHeaderRightView>\n      ) : null}\n      {backButtonImage !== undefined ? (\n        <ScreenStackHeaderBackButtonImage\n          key=\"backImage\"\n          source={backButtonImage}\n        />\n      ) : null}\n      {headerLeft !== undefined ? (\n        <ScreenStackHeaderLeftView>\n          {headerLeft({ tintColor })}\n        </ScreenStackHeaderLeftView>\n      ) : null}\n      {headerCenter !== undefined ? (\n        <ScreenStackHeaderCenterView>\n          {headerCenter({ tintColor })}\n        </ScreenStackHeaderCenterView>\n      ) : null}\n      {isSearchBarAvailableForCurrentPlatform &&\n      processedSearchBarOptions !== undefined ? (\n        <ScreenStackHeaderSearchBarView>\n          <SearchBar {...processedSearchBarOptions} />\n        </ScreenStackHeaderSearchBarView>\n      ) : null}\n    </ScreenStackHeaderConfig>\n  );\n}\n"]}
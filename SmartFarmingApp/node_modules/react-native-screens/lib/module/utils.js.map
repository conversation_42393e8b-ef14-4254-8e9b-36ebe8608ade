{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "Platform", "isSearchBarAvailableForCurrentPlatform", "includes", "OS", "executeNativeBackPress", "exitApp", "isNewBackTitleImplementation"], "sourceRoot": "../../src", "sources": ["utils.ts"], "mappings": "AAAA,SAASA,WAAW,EAAEC,QAAQ,QAAQ,cAAc;AAEpD,OAAO,MAAMC,sCAAsC,GAAG,CACpD,KAAK,EACL,SAAS,CACV,CAACC,QAAQ,CAACF,QAAQ,CAACG,EAAE,CAAC;AAEvB,OAAO,SAASC,sBAAsBA,CAAA,EAAG;EACvC;EACAL,WAAW,CAACM,OAAO,CAAC,CAAC;EACrB,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMC,4BAA4B,GAAG,IAAI"}
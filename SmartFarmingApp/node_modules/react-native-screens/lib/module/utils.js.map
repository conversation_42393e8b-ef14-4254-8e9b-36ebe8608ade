{"version": 3, "sources": ["utils.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "Platform", "isSearchBarAvailableForCurrentPlatform", "includes", "OS", "executeNativeBackPress", "exitApp"], "mappings": "AAAA,SAASA,WAAT,EAAsBC,QAAtB,QAAsC,cAAtC;AAEA,OAAO,MAAMC,sCAAsC,GAAG,CACpD,KADoD,EAEpD,SAFoD,EAGpDC,QAHoD,CAG3CF,QAAQ,CAACG,EAHkC,CAA/C;AAKP,OAAO,SAASC,sBAAT,GAAkC;AACvC;AACAL,EAAAA,WAAW,CAACM,OAAZ;AACA,SAAO,IAAP;AACD", "sourcesContent": ["import { BackHandler, Platform } from 'react-native';\n\nexport const isSearchBarAvailableForCurrentPlatform = [\n  'ios',\n  'android',\n].includes(Platform.OS);\n\nexport function executeNativeBackPress() {\n  // This function invokes the native back press event\n  BackHandler.exitApp();\n  return true;\n}\n"]}
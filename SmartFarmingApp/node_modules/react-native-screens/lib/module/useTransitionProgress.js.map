{"version": 3, "sources": ["useTransitionProgress.tsx"], "names": ["React", "TransitionProgressContext", "useTransitionProgress", "progress", "useContext", "undefined", "Error"], "mappings": "AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AAEA,OAAOC,yBAAP,MAAsC,6BAAtC;AAEA,eAAe,SAASC,qBAAT,GAAiC;AAC9C,QAAMC,QAAQ,GAAGH,KAAK,CAACI,UAAN,CAAiBH,yBAAjB,CAAjB;;AAEA,MAAIE,QAAQ,KAAKE,SAAjB,EAA4B;AAC1B,UAAM,IAAIC,KAAJ,CACJ,wFADI,CAAN;AAGD;;AAED,SAAOH,QAAP;AACD", "sourcesContent": ["import * as React from 'react';\n\nimport TransitionProgressContext from './TransitionProgressContext';\n\nexport default function useTransitionProgress() {\n  const progress = React.useContext(TransitionProgressContext);\n\n  if (progress === undefined) {\n    throw new Error(\n      \"Couldn't find values for transition progress. Are you inside a screen in Native Stack?\"\n    );\n  }\n\n  return progress;\n}\n"]}
{"version": 3, "sources": ["index.native.tsx"], "names": ["React", "Animated", "Image", "Platform", "requireNativeComponent", "StyleSheet", "UIManager", "View", "Freeze", "version", "TransitionProgressContext", "useTransitionProgress", "isSearchBarAvailableForCurrentPlatform", "executeNativeBackPress", "isPlatformSupported", "OS", "ENABLE_SCREENS", "enableScreens", "shouldEnableScreens", "getViewManagerConfig", "console", "error", "ENABLE_FREEZE", "enableFreeze", "shouldEnableReactFreeze", "minor", "parseInt", "split", "warn", "shouldUseActivityState", "screensEnabled", "NativeScreenValue", "NativeScreenContainerValue", "NativeScreenNavigationContainerValue", "NativeScreenStack", "NativeScreenStackHeaderConfig", "NativeScreenStackHeaderSubview", "AnimatedNativeScreen", "NativeSearchBar", "NativeFullWindowOverlay", "ScreensNativeModules", "NativeScreen", "NativeScreenContainer", "NativeScreenNavigationContainer", "DelayedFreeze", "freeze", "children", "freezeState", "setFreezeState", "useState", "setImmediate", "ScreenStack", "props", "rest", "size", "Children", "count", "childrenWithFreeze", "map", "child", "index", "key", "descriptor", "descriptors", "freezeEnabled", "options", "freezeOnBlur", "InnerScreen", "Component", "Value", "ref", "onComponentRef", "setNativeProps", "render", "enabled", "createAnimatedComponent", "active", "activityState", "isNativeStack", "gestureResponseDistance", "undefined", "handleRef", "viewConfig", "validAttributes", "style", "display", "setRef", "start", "end", "top", "bottom", "event", "nativeEvent", "progress", "closing", "goingForward", "useNativeDriver", "ScreenContainer", "hasTwoStates", "styles", "create", "headerSubview", "position", "right", "flexDirection", "alignItems", "justifyContent", "ScreenStackHeaderBackButtonImage", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderSearchBarView", "ScreenContext", "createContext", "Screen", "ScreenWrapper", "context", "module", "exports", "ScreenStackHeaderConfig", "ScreenStackHeaderSubview", "SearchBar", "FullWindowOverlay"], "mappings": ";;;;AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,SACEC,QADF,EAEEC,KAFF,EAIEC,QAJF,EAKEC,sBALF,EAMEC,UANF,EAOEC,SAPF,EAQEC,IARF,QAUO,cAVP;AAWA,SAASC,MAAT,QAAuB,cAAvB;AACA,SAASC,OAAT,QAAwB,2BAAxB;AAEA,OAAOC,yBAAP,MAAsC,6BAAtC;AACA,OAAOC,qBAAP,MAAkC,yBAAlC;AAcA,SACEC,sCADF,EAEEC,sBAFF,QAGO,SAHP,C,CAKA;;AACA,MAAMC,mBAAmB,GACvBX,QAAQ,CAACY,EAAT,KAAgB,KAAhB,IACAZ,QAAQ,CAACY,EAAT,KAAgB,SADhB,IAEAZ,QAAQ,CAACY,EAAT,KAAgB,SAHlB;AAKA,IAAIC,cAAc,GAAGF,mBAArB;;AAEA,SAASG,aAAT,CAAuBC,mBAAmB,GAAG,IAA7C,EAAyD;AACvDF,EAAAA,cAAc,GAAGF,mBAAmB,IAAII,mBAAxC;;AACA,MAAIF,cAAc,IAAI,CAACV,SAAS,CAACa,oBAAV,CAA+B,WAA/B,CAAvB,EAAoE;AAClEC,IAAAA,OAAO,CAACC,KAAR,CACG,wGADH;AAGD;AACF;;AAED,IAAIC,aAAa,GAAG,KAApB;;AAEA,SAASC,YAAT,CAAsBC,uBAAuB,GAAG,IAAhD,EAA4D;AAC1D,QAAMC,KAAK,GAAGC,QAAQ,CAACjB,OAAO,CAACkB,KAAR,CAAc,GAAd,EAAmB,CAAnB,CAAD,CAAtB,CAD0D,CACX;AAE/C;;AACA,MAAI,EAAEF,KAAK,KAAK,CAAV,IAAeA,KAAK,IAAI,EAA1B,KAAiCD,uBAArC,EAA8D;AAC5DJ,IAAAA,OAAO,CAACQ,IAAR,CACE,kIADF;AAGD;;AAEDN,EAAAA,aAAa,GAAGE,uBAAhB;AACD,C,CAED;;;AACA,MAAMK,sBAAsB,GAAG,IAA/B;;AAEA,SAASC,cAAT,GAAmC;AACjC,SAAOd,cAAP;AACD,C,CAED;AACA;;;AACA,IAAIe,iBAAJ;AACA,IAAIC,0BAAJ;AACA,IAAIC,oCAAJ;AACA,IAAIC,iBAAJ;AACA,IAAIC,6BAAJ;AACA,IAAIC,8BAAJ;AAGA,IAAIC,oBAAJ;AACA,IAAIC,eAAJ;AACA,IAAIC,uBAAJ;AAEA,MAAMC,oBAAoB,GAAG;AAC3B,MAAIC,YAAJ,GAAmB;AACjBV,IAAAA,iBAAiB,GACfA,iBAAiB,IAAI3B,sBAAsB,CAAC,WAAD,CAD7C;AAEA,WAAO2B,iBAAP;AACD,GAL0B;;AAO3B,MAAIW,qBAAJ,GAA4B;AAC1BV,IAAAA,0BAA0B,GACxBA,0BAA0B,IAC1B5B,sBAAsB,CAAC,oBAAD,CAFxB;AAGA,WAAO4B,0BAAP;AACD,GAZ0B;;AAc3B,MAAIW,+BAAJ,GAAsC;AACpCV,IAAAA,oCAAoC,GAClCA,oCAAoC,KACnC9B,QAAQ,CAACY,EAAT,KAAgB,KAAhB,GACGX,sBAAsB,CAAC,8BAAD,CADzB,GAEG,KAAKsC,qBAH2B,CADtC;AAKA,WAAOT,oCAAP;AACD,GArB0B;;AAuB3B,MAAIC,iBAAJ,GAAwB;AACtBA,IAAAA,iBAAiB,GACfA,iBAAiB,IAAI9B,sBAAsB,CAAC,gBAAD,CAD7C;AAEA,WAAO8B,iBAAP;AACD,GA3B0B;;AA6B3B,MAAIC,6BAAJ,GAAoC;AAClCA,IAAAA,6BAA6B,GAC3BA,6BAA6B,IAC7B/B,sBAAsB,CAAC,4BAAD,CAFxB;AAGA,WAAO+B,6BAAP;AACD,GAlC0B;;AAoC3B,MAAIC,8BAAJ,GAAqC;AACnCA,IAAAA,8BAA8B,GAC5BA,8BAA8B,IAC9BhC,sBAAsB,CAAC,6BAAD,CAFxB;AAGA,WAAOgC,8BAAP;AACD,GAzC0B;;AA2C3B,MAAIE,eAAJ,GAAsB;AACpBA,IAAAA,eAAe,GAAGA,eAAe,IAAIlC,sBAAsB,CAAC,cAAD,CAA3D;AACA,WAAOkC,eAAP;AACD,GA9C0B;;AAgD3B,MAAIC,uBAAJ,GAA8B;AAC5BA,IAAAA,uBAAuB,GACrBA,uBAAuB,IAAInC,sBAAsB,CAAC,sBAAD,CADnD;AAEA,WAAOmC,uBAAP;AACD;;AApD0B,CAA7B;;AA4DA;AACA;AACA,SAASK,aAAT,CAAuB;AAAEC,EAAAA,MAAF;AAAUC,EAAAA;AAAV,CAAvB,EAAiE;AAC/D;AACA,QAAM,CAACC,WAAD,EAAcC,cAAd,IAAgChD,KAAK,CAACiD,QAAN,CAAe,KAAf,CAAtC;;AAEA,MAAIJ,MAAM,KAAKE,WAAf,EAA4B;AAC1B;AACA;AACAG,IAAAA,YAAY,CAAC,MAAM;AACjBF,MAAAA,cAAc,CAACH,MAAD,CAAd;AACD,KAFW,CAAZ;AAGD;;AAED,sBAAO,oBAAC,MAAD;AAAQ,IAAA,MAAM,EAAEA,MAAM,GAAGE,WAAH,GAAiB;AAAvC,KAA+CD,QAA/C,CAAP;AACD;;AAED,SAASK,WAAT,CAAqBC,KAArB,EAA8C;AAC5C,QAAM;AAAEN,IAAAA,QAAF;AAAY,OAAGO;AAAf,MAAwBD,KAA9B;AACA,QAAME,IAAI,GAAGtD,KAAK,CAACuD,QAAN,CAAeC,KAAf,CAAqBV,QAArB,CAAb,CAF4C,CAG5C;;AACA,QAAMW,kBAAkB,GAAGzD,KAAK,CAACuD,QAAN,CAAeG,GAAf,CAAmBZ,QAAnB,EAA6B,CAACa,KAAD,EAAQC,KAAR,KAAkB;AAAA;;AACxE;AACA,UAAM;AAAER,MAAAA,KAAF;AAASS,MAAAA;AAAT,QAAiBF,KAAvB;AACA,UAAMG,UAAU,wBAAGV,KAAH,aAAGA,KAAH,uBAAGA,KAAK,CAAEU,UAAV,iEAAwBV,KAAxB,aAAwBA,KAAxB,6CAAwBA,KAAK,CAAEW,WAA/B,uDAAwB,mBAAqBF,GAArB,CAAxC;AACA,UAAMG,aAAa,4BAAGF,UAAH,aAAGA,UAAH,8CAAGA,UAAU,CAAEG,OAAf,wDAAG,oBAAqBC,YAAxB,yEAAwC5C,aAA3D;AAEA,wBACE,oBAAC,aAAD;AAAe,MAAA,MAAM,EAAE0C,aAAa,IAAIV,IAAI,GAAGM,KAAP,GAAe;AAAvD,OACGD,KADH,CADF;AAKD,GAX0B,CAA3B;AAaA,sBACE,oBAAC,oBAAD,CAAsB,iBAAtB,EAA4CN,IAA5C,EACGI,kBADH,CADF;AAKD,C,CAED;AACA;;;AAWA,MAAMU,WAAN,SAA0BnE,KAAK,CAACoE,SAAhC,CAAuD;AAAA;AAAA;;AAAA,iCACD,IADC;;AAAA,qCAEnC,IAAInE,QAAQ,CAACoE,KAAb,CAAmB,CAAnB,CAFmC;;AAAA,sCAGlC,IAAIpE,QAAQ,CAACoE,KAAb,CAAmB,CAAnB,CAHkC;;AAAA,0CAI9B,IAAIpE,QAAQ,CAACoE,KAAb,CAAmB,CAAnB,CAJ8B;;AAAA,oCAU3CC,GAAD,IAAqD;AAAA;;AAC5D,WAAKA,GAAL,GAAWA,GAAX;AACA,mDAAKlB,KAAL,EAAWmB,cAAX,kGAA4BD,GAA5B;AACD,KAboD;AAAA;;AAMrDE,EAAAA,cAAc,CAACpB,KAAD,EAA2B;AAAA;;AACvC,sBAAKkB,GAAL,wDAAUE,cAAV,CAAyBpB,KAAzB;AACD;;AAODqB,EAAAA,MAAM,GAAG;AACP,UAAM;AACJC,MAAAA,OAAO,GAAG1D,cADN;AAEJkD,MAAAA,YAAY,GAAG5C,aAFX;AAGJ,SAAG+B;AAHC,QAIF,KAAKD,KAJT;;AAMA,QAAIsB,OAAO,IAAI5D,mBAAf,EAAoC;AAAA;;AAClCuB,MAAAA,oBAAoB,GAClBA,oBAAoB,IACpBpC,QAAQ,CAAC0E,uBAAT,CAAiCnC,oBAAoB,CAACC,YAAtD,CAFF;AAIA,UAAI;AACF;AACA;AACA;AACAmC,QAAAA,MAJE;AAKFC,QAAAA,aALE;AAMF/B,QAAAA,QANE;AAOFgC,QAAAA,aAPE;AAQFC,QAAAA,uBARE;AASF,WAAG3B;AATD,UAUAC,IAVJ;;AAYA,UAAIuB,MAAM,KAAKI,SAAX,IAAwBH,aAAa,KAAKG,SAA9C,EAAyD;AACvD5D,QAAAA,OAAO,CAACQ,IAAR,CACE,+QADF;AAGAiD,QAAAA,aAAa,GAAGD,MAAM,KAAK,CAAX,GAAe,CAAf,GAAmB,CAAnC,CAJuD,CAIjB;AACvC;;AAED,YAAMK,SAAS,GAAIX,GAAD,IAAqB;AAAA;;AACrC,YAAIA,GAAJ,aAAIA,GAAJ,kCAAIA,GAAG,CAAEY,UAAT,qEAAI,gBAAiBC,eAArB,kDAAI,sBAAkCC,KAAtC,EAA6C;AAC3Cd,UAAAA,GAAG,CAACY,UAAJ,CAAeC,eAAf,CAA+BC,KAA/B,GAAuC,EACrC,GAAGd,GAAG,CAACY,UAAJ,CAAeC,eAAf,CAA+BC,KADG;AAErCC,YAAAA,OAAO,EAAE;AAF4B,WAAvC;AAIA,eAAKC,MAAL,CAAYhB,GAAZ;AACD;AACF,OARD;;AAUA,0BACE,oBAAC,aAAD;AAAe,QAAA,MAAM,EAAEJ,YAAY,IAAIW,aAAa,KAAK;AAAzD,sBACE,oBAAC,oBAAD,eACMzB,KADN;AAEE,QAAA,aAAa,EAAEyB,aAFjB;AAGE,QAAA,uBAAuB,EAAE;AACvBU,UAAAA,KAAK,2BAAER,uBAAF,aAAEA,uBAAF,uBAAEA,uBAAuB,CAAEQ,KAA3B,yEAAoC,CAAC,CADnB;AAEvBC,UAAAA,GAAG,4BAAET,uBAAF,aAAEA,uBAAF,uBAAEA,uBAAuB,CAAES,GAA3B,2EAAkC,CAAC,CAFf;AAGvBC,UAAAA,GAAG,4BAAEV,uBAAF,aAAEA,uBAAF,uBAAEA,uBAAuB,CAAEU,GAA3B,2EAAkC,CAAC,CAHf;AAIvBC,UAAAA,MAAM,4BAAEX,uBAAF,aAAEA,uBAAF,uBAAEA,uBAAuB,CAAEW,MAA3B,2EAAqC,CAAC;AAJrB,SAH3B,CASE;AACA;AAVF;AAWE,QAAA,GAAG,EAAET,SAXP;AAYE,QAAA,oBAAoB,EAClB,CAACH,aAAD,GACIE,SADJ,GAEI/E,QAAQ,CAAC0F,KAAT,CACE,CACE;AACEC,UAAAA,WAAW,EAAE;AACXC,YAAAA,QAAQ,EAAE,KAAKA,QADJ;AAEXC,YAAAA,OAAO,EAAE,KAAKA,OAFH;AAGXC,YAAAA,YAAY,EAAE,KAAKA;AAHR;AADf,SADF,CADF,EAUE;AAAEC,UAAAA,eAAe,EAAE;AAAnB,SAVF;AAfR,UA4BG,CAAClB,aAAD,GAAmB;AAClBhC,MAAAA,QADD,gBAGC,oBAAC,yBAAD,CAA2B,QAA3B;AACE,QAAA,KAAK,EAAE;AACL+C,UAAAA,QAAQ,EAAE,KAAKA,QADV;AAELC,UAAAA,OAAO,EAAE,KAAKA,OAFT;AAGLC,UAAAA,YAAY,EAAE,KAAKA;AAHd;AADT,SAMGjD,QANH,CA/BJ,CADF,CADF;AA6CD,KA/ED,MA+EO;AACL;AACA,UAAI;AACF8B,QAAAA,MADE;AAEFC,QAAAA,aAFE;AAGFO,QAAAA,KAHE;AAIF;AACAb,QAAAA,cALE;AAMF,WAAGnB;AAND,UAOAC,IAPJ;;AASA,UAAIuB,MAAM,KAAKI,SAAX,IAAwBH,aAAa,KAAKG,SAA9C,EAAyD;AACvDH,QAAAA,aAAa,GAAGD,MAAM,KAAK,CAAX,GAAe,CAAf,GAAmB,CAAnC;AACD;;AACD,0BACE,oBAAC,QAAD,CAAU,IAAV;AACE,QAAA,KAAK,EAAE,CAACQ,KAAD,EAAQ;AAAEC,UAAAA,OAAO,EAAER,aAAa,KAAK,CAAlB,GAAsB,MAAtB,GAA+B;AAA1C,SAAR,CADT;AAEE,QAAA,GAAG,EAAE,KAAKS;AAFZ,SAGMlC,KAHN,EADF;AAOD;AACF;;AA3HoD;;AA8HvD,SAAS6C,eAAT,CAAyB7C,KAAzB,EAAsD;AACpD,QAAM;AAAEsB,IAAAA,OAAO,GAAG1D,cAAZ;AAA4BkF,IAAAA,YAA5B;AAA0C,OAAG7C;AAA7C,MAAsDD,KAA5D;;AAEA,MAAIsB,OAAO,IAAI5D,mBAAf,EAAoC;AAClC,QAAIoF,YAAJ,EAAkB;AAChB,0BAAO,oBAAC,oBAAD,CAAsB,+BAAtB,EAA0D7C,IAA1D,CAAP;AACD;;AACD,wBAAO,oBAAC,oBAAD,CAAsB,qBAAtB,EAAgDA,IAAhD,CAAP;AACD;;AACD,sBAAO,oBAAC,IAAD,EAAUA,IAAV,CAAP;AACD;;AAED,MAAM8C,MAAM,GAAG9F,UAAU,CAAC+F,MAAX,CAAkB;AAC/BC,EAAAA,aAAa,EAAE;AACbC,IAAAA,QAAQ,EAAE,UADG;AAEbb,IAAAA,GAAG,EAAE,CAFQ;AAGbc,IAAAA,KAAK,EAAE,CAHM;AAIbC,IAAAA,aAAa,EAAE,KAJF;AAKbC,IAAAA,UAAU,EAAE,QALC;AAMbC,IAAAA,cAAc,EAAE;AANH;AADgB,CAAlB,CAAf;;AAWA,MAAMC,gCAAgC,GAAIvD,KAAD,iBACvC,oBAAC,oBAAD,CAAsB,8BAAtB;AACE,EAAA,IAAI,EAAC,MADP;AAEE,EAAA,KAAK,EAAE+C,MAAM,CAACE;AAFhB,gBAGE,oBAAC,KAAD;AAAO,EAAA,UAAU,EAAC,QAAlB;AAA2B,EAAA,YAAY,EAAE;AAAzC,GAAgDjD,KAAhD,EAHF,CADF;;AAQA,MAAMwD,0BAA0B,GAC9BxD,KADiC,iBAGjC,oBAAC,oBAAD,CAAsB,8BAAtB,eACMA,KADN;AAEE,EAAA,IAAI,EAAC,OAFP;AAGE,EAAA,KAAK,EAAE+C,MAAM,CAACE;AAHhB,GAHF;;AAUA,MAAMQ,yBAAyB,GAC7BzD,KADgC,iBAGhC,oBAAC,oBAAD,CAAsB,8BAAtB,eACMA,KADN;AAEE,EAAA,IAAI,EAAC,MAFP;AAGE,EAAA,KAAK,EAAE+C,MAAM,CAACE;AAHhB,GAHF;;AAUA,MAAMS,2BAA2B,GAC/B1D,KADkC,iBAGlC,oBAAC,oBAAD,CAAsB,8BAAtB,eACMA,KADN;AAEE,EAAA,IAAI,EAAC,QAFP;AAGE,EAAA,KAAK,EAAE+C,MAAM,CAACE;AAHhB,GAHF;;AAUA,MAAMU,8BAA8B,GAClC3D,KADqC,iBAGrC,oBAAC,oBAAD,CAAsB,8BAAtB,eACMA,KADN;AAEE,EAAA,IAAI,EAAC,WAFP;AAGE,EAAA,KAAK,EAAE+C,MAAM,CAACE;AAHhB,GAHF;;AAwBA;AACA;AACA,MAAMW,aAAa,gBAAGhH,KAAK,CAACiH,aAAN,CAAoB9C,WAApB,CAAtB;;AAEA,MAAM+C,MAAN,SAAqBlH,KAAK,CAACoE,SAA3B,CAAkD;AAGhDK,EAAAA,MAAM,GAAG;AACP,UAAM0C,aAAa,GAAG,KAAKC,OAAL,IAAgBjD,WAAtC;AACA,wBAAO,oBAAC,aAAD,EAAmB,KAAKf,KAAxB,CAAP;AACD;;AAN+C;;gBAA5C8D,M,iBACiBF,a;;AAQvBK,MAAM,CAACC,OAAP,GAAiB;AACf;AACA;AACAJ,EAAAA,MAHe;AAIfjB,EAAAA,eAJe;AAKfe,EAAAA,aALe;AAMf7D,EAAAA,WANe;AAOfgB,EAAAA,WAPe;;AASf,MAAI1B,YAAJ,GAAmB;AACjB,WAAOD,oBAAoB,CAACC,YAA5B;AACD,GAXc;;AAaf,MAAIC,qBAAJ,GAA4B;AAC1B,WAAOF,oBAAoB,CAACE,qBAA5B;AACD,GAfc;;AAiBf,MAAIC,+BAAJ,GAAsC;AACpC,WAAOH,oBAAoB,CAACG,+BAA5B;AACD,GAnBc;;AAqBf,MAAI4E,uBAAJ,GAA8B;AAC5B,WAAO/E,oBAAoB,CAACL,6BAA5B;AACD,GAvBc;;AAwBf,MAAIqF,wBAAJ,GAA+B;AAC7B,WAAOhF,oBAAoB,CAACJ,8BAA5B;AACD,GA1Bc;;AA2Bf,MAAIqF,SAAJ,GAAgB;AACd,QAAI,CAAC7G,sCAAL,EAA6C;AAC3CQ,MAAAA,OAAO,CAACQ,IAAR,CACE,+DADF;AAGA,aAAOrB,IAAP;AACD;;AAED,WAAOiC,oBAAoB,CAACF,eAA5B;AACD,GApCc;;AAqCf,MAAIoF,iBAAJ,GAAwB;AACtB,QAAIvH,QAAQ,CAACY,EAAT,KAAgB,KAApB,EAA2B;AACzBK,MAAAA,OAAO,CAACQ,IAAR,CAAa,2DAAb;AACA,aAAOrB,IAAP;AACD;;AAED,WAAOiC,oBAAoB,CAACD,uBAA5B;AACD,GA5Cc;;AA6Cf;AACA;AACAoE,EAAAA,gCA/Ce;AAgDfC,EAAAA,0BAhDe;AAiDfC,EAAAA,yBAjDe;AAkDfC,EAAAA,2BAlDe;AAmDfC,EAAAA,8BAnDe;AAqDf9F,EAAAA,aArDe;AAsDfM,EAAAA,YAtDe;AAuDfO,EAAAA,cAvDe;AAwDfD,EAAAA,sBAxDe;AAyDflB,EAAAA,qBAzDe;AA2DfC,EAAAA,sCA3De;AA4DfC,EAAAA;AA5De,CAAjB", "sourcesContent": ["import React from 'react';\nimport {\n  Animated,\n  Image,\n  ImageProps,\n  Platform,\n  requireNativeComponent,\n  StyleSheet,\n  UIManager,\n  View,\n  ViewProps,\n} from 'react-native';\nimport { Freeze } from 'react-freeze';\nimport { version } from 'react-native/package.json';\n\nimport TransitionProgressContext from './TransitionProgressContext';\nimport useTransitionProgress from './useTransitionProgress';\nimport {\n  StackPresentationTypes,\n  StackAnimationTypes,\n  BlurEffectTypes,\n  ScreenReplaceTypes,\n  ScreenOrientationTypes,\n  HeaderSubviewTypes,\n  ScreenProps,\n  ScreenContainerProps,\n  ScreenStackProps,\n  ScreenStackHeaderConfigProps,\n  SearchBarProps,\n} from './types';\nimport {\n  isSearchBarAvailableForCurrentPlatform,\n  executeNativeBackPress,\n} from './utils';\n\n// web implementation is taken from `index.tsx`\nconst isPlatformSupported =\n  Platform.OS === 'ios' ||\n  Platform.OS === 'android' ||\n  Platform.OS === 'windows';\n\nlet ENABLE_SCREENS = isPlatformSupported;\n\nfunction enableScreens(shouldEnableScreens = true): void {\n  ENABLE_SCREENS = isPlatformSupported && shouldEnableScreens;\n  if (ENABLE_SCREENS && !UIManager.getViewManagerConfig('RNSScreen')) {\n    console.error(\n      `Screen native module hasn't been linked. Please check the react-native-screens README for more details`\n    );\n  }\n}\n\nlet ENABLE_FREEZE = false;\n\nfunction enableFreeze(shouldEnableReactFreeze = true): void {\n  const minor = parseInt(version.split('.')[1]); // eg. takes 66 from '0.66.0'\n\n  // react-freeze requires react-native >=0.64, react-native from main is 0.0.0\n  if (!(minor === 0 || minor >= 64) && shouldEnableReactFreeze) {\n    console.warn(\n      'react-freeze library requires at least react-native 0.64. Please upgrade your react-native version in order to use this feature.'\n    );\n  }\n\n  ENABLE_FREEZE = shouldEnableReactFreeze;\n}\n\n// const that tells if the library should use new implementation, will be undefined for older versions\nconst shouldUseActivityState = true;\n\nfunction screensEnabled(): boolean {\n  return ENABLE_SCREENS;\n}\n\n// We initialize these lazily so that importing the module doesn't throw error when not linked\n// This is necessary coz libraries such as React Navigation import the library where it may not be enabled\nlet NativeScreenValue: React.ComponentType<ScreenProps>;\nlet NativeScreenContainerValue: React.ComponentType<ScreenContainerProps>;\nlet NativeScreenNavigationContainerValue: React.ComponentType<ScreenContainerProps>;\nlet NativeScreenStack: React.ComponentType<ScreenStackProps>;\nlet NativeScreenStackHeaderConfig: React.ComponentType<ScreenStackHeaderConfigProps>;\nlet NativeScreenStackHeaderSubview: React.ComponentType<React.PropsWithChildren<\n  ViewProps & { type?: HeaderSubviewTypes }\n>>;\nlet AnimatedNativeScreen: React.ComponentType<ScreenProps>;\nlet NativeSearchBar: React.ComponentType<SearchBarProps>;\nlet NativeFullWindowOverlay: React.ComponentType<View>;\n\nconst ScreensNativeModules = {\n  get NativeScreen() {\n    NativeScreenValue =\n      NativeScreenValue || requireNativeComponent('RNSScreen');\n    return NativeScreenValue;\n  },\n\n  get NativeScreenContainer() {\n    NativeScreenContainerValue =\n      NativeScreenContainerValue ||\n      requireNativeComponent('RNSScreenContainer');\n    return NativeScreenContainerValue;\n  },\n\n  get NativeScreenNavigationContainer() {\n    NativeScreenNavigationContainerValue =\n      NativeScreenNavigationContainerValue ||\n      (Platform.OS === 'ios'\n        ? requireNativeComponent('RNSScreenNavigationContainer')\n        : this.NativeScreenContainer);\n    return NativeScreenNavigationContainerValue;\n  },\n\n  get NativeScreenStack() {\n    NativeScreenStack =\n      NativeScreenStack || requireNativeComponent('RNSScreenStack');\n    return NativeScreenStack;\n  },\n\n  get NativeScreenStackHeaderConfig() {\n    NativeScreenStackHeaderConfig =\n      NativeScreenStackHeaderConfig ||\n      requireNativeComponent('RNSScreenStackHeaderConfig');\n    return NativeScreenStackHeaderConfig;\n  },\n\n  get NativeScreenStackHeaderSubview() {\n    NativeScreenStackHeaderSubview =\n      NativeScreenStackHeaderSubview ||\n      requireNativeComponent('RNSScreenStackHeaderSubview');\n    return NativeScreenStackHeaderSubview;\n  },\n\n  get NativeSearchBar() {\n    NativeSearchBar = NativeSearchBar || requireNativeComponent('RNSSearchBar');\n    return NativeSearchBar;\n  },\n\n  get NativeFullWindowOverlay() {\n    NativeFullWindowOverlay =\n      NativeFullWindowOverlay || requireNativeComponent('RNSFullWindowOverlay');\n    return NativeFullWindowOverlay;\n  },\n};\n\ninterface FreezeWrapperProps {\n  freeze: boolean;\n  children: React.ReactNode;\n}\n\n// This component allows one more render before freezing the screen.\n// Allows activityState to reach the native side and useIsFocused to work correctly.\nfunction DelayedFreeze({ freeze, children }: FreezeWrapperProps) {\n  // flag used for determining whether freeze should be enabled\n  const [freezeState, setFreezeState] = React.useState(false);\n\n  if (freeze !== freezeState) {\n    // setImmediate is executed at the end of the JS execution block.\n    // Used here for changing the state right after the render.\n    setImmediate(() => {\n      setFreezeState(freeze);\n    });\n  }\n\n  return <Freeze freeze={freeze ? freezeState : false}>{children}</Freeze>;\n}\n\nfunction ScreenStack(props: ScreenStackProps) {\n  const { children, ...rest } = props;\n  const size = React.Children.count(children);\n  // freezes all screens except the top one\n  const childrenWithFreeze = React.Children.map(children, (child, index) => {\n    // @ts-expect-error it's either SceneView in v6 or RouteView in v5\n    const { props, key } = child;\n    const descriptor = props?.descriptor ?? props?.descriptors?.[key];\n    const freezeEnabled = descriptor?.options?.freezeOnBlur ?? ENABLE_FREEZE;\n\n    return (\n      <DelayedFreeze freeze={freezeEnabled && size - index > 1}>\n        {child}\n      </DelayedFreeze>\n    );\n  });\n\n  return (\n    <ScreensNativeModules.NativeScreenStack {...rest}>\n      {childrenWithFreeze}\n    </ScreensNativeModules.NativeScreenStack>\n  );\n}\n\n// Incomplete type, all accessible properties available at:\n// react-native/Libraries/Components/View/ReactNativeViewViewConfig.js\ninterface ViewConfig extends View {\n  viewConfig: {\n    validAttributes: {\n      style: {\n        display: boolean;\n      };\n    };\n  };\n}\n\nclass InnerScreen extends React.Component<ScreenProps> {\n  private ref: React.ElementRef<typeof View> | null = null;\n  private closing = new Animated.Value(0);\n  private progress = new Animated.Value(0);\n  private goingForward = new Animated.Value(0);\n\n  setNativeProps(props: ScreenProps): void {\n    this.ref?.setNativeProps(props);\n  }\n\n  setRef = (ref: React.ElementRef<typeof View> | null): void => {\n    this.ref = ref;\n    this.props.onComponentRef?.(ref);\n  };\n\n  render() {\n    const {\n      enabled = ENABLE_SCREENS,\n      freezeOnBlur = ENABLE_FREEZE,\n      ...rest\n    } = this.props;\n\n    if (enabled && isPlatformSupported) {\n      AnimatedNativeScreen =\n        AnimatedNativeScreen ||\n        Animated.createAnimatedComponent(ScreensNativeModules.NativeScreen);\n\n      let {\n        // Filter out active prop in this case because it is unused and\n        // can cause problems depending on react-native version:\n        // https://github.com/react-navigation/react-navigation/issues/4886\n        active,\n        activityState,\n        children,\n        isNativeStack,\n        gestureResponseDistance,\n        ...props\n      } = rest;\n\n      if (active !== undefined && activityState === undefined) {\n        console.warn(\n          'It appears that you are using old version of react-navigation library. Please update @react-navigation/bottom-tabs, @react-navigation/stack and @react-navigation/drawer to version 5.10.0 or above to take full advantage of new functionality added to react-native-screens'\n        );\n        activityState = active !== 0 ? 2 : 0; // in the new version, we need one of the screens to have value of 2 after the transition\n      }\n\n      const handleRef = (ref: ViewConfig) => {\n        if (ref?.viewConfig?.validAttributes?.style) {\n          ref.viewConfig.validAttributes.style = {\n            ...ref.viewConfig.validAttributes.style,\n            display: false,\n          };\n          this.setRef(ref);\n        }\n      };\n\n      return (\n        <DelayedFreeze freeze={freezeOnBlur && activityState === 0}>\n          <AnimatedNativeScreen\n            {...props}\n            activityState={activityState}\n            gestureResponseDistance={{\n              start: gestureResponseDistance?.start ?? -1,\n              end: gestureResponseDistance?.end ?? -1,\n              top: gestureResponseDistance?.top ?? -1,\n              bottom: gestureResponseDistance?.bottom ?? -1,\n            }}\n            // This prevents showing blank screen when navigating between multiple screens with freezing\n            // https://github.com/software-mansion/react-native-screens/pull/1208\n            ref={handleRef}\n            onTransitionProgress={\n              !isNativeStack\n                ? undefined\n                : Animated.event(\n                    [\n                      {\n                        nativeEvent: {\n                          progress: this.progress,\n                          closing: this.closing,\n                          goingForward: this.goingForward,\n                        },\n                      },\n                    ],\n                    { useNativeDriver: true }\n                  )\n            }>\n            {!isNativeStack ? ( // see comment of this prop in types.tsx for information why it is needed\n              children\n            ) : (\n              <TransitionProgressContext.Provider\n                value={{\n                  progress: this.progress,\n                  closing: this.closing,\n                  goingForward: this.goingForward,\n                }}>\n                {children}\n              </TransitionProgressContext.Provider>\n            )}\n          </AnimatedNativeScreen>\n        </DelayedFreeze>\n      );\n    } else {\n      // same reason as above\n      let {\n        active,\n        activityState,\n        style,\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        onComponentRef,\n        ...props\n      } = rest;\n\n      if (active !== undefined && activityState === undefined) {\n        activityState = active !== 0 ? 2 : 0;\n      }\n      return (\n        <Animated.View\n          style={[style, { display: activityState !== 0 ? 'flex' : 'none' }]}\n          ref={this.setRef}\n          {...props}\n        />\n      );\n    }\n  }\n}\n\nfunction ScreenContainer(props: ScreenContainerProps) {\n  const { enabled = ENABLE_SCREENS, hasTwoStates, ...rest } = props;\n\n  if (enabled && isPlatformSupported) {\n    if (hasTwoStates) {\n      return <ScreensNativeModules.NativeScreenNavigationContainer {...rest} />;\n    }\n    return <ScreensNativeModules.NativeScreenContainer {...rest} />;\n  }\n  return <View {...rest} />;\n}\n\nconst styles = StyleSheet.create({\n  headerSubview: {\n    position: 'absolute',\n    top: 0,\n    right: 0,\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n});\n\nconst ScreenStackHeaderBackButtonImage = (props: ImageProps): JSX.Element => (\n  <ScreensNativeModules.NativeScreenStackHeaderSubview\n    type=\"back\"\n    style={styles.headerSubview}>\n    <Image resizeMode=\"center\" fadeDuration={0} {...props} />\n  </ScreensNativeModules.NativeScreenStackHeaderSubview>\n);\n\nconst ScreenStackHeaderRightView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => (\n  <ScreensNativeModules.NativeScreenStackHeaderSubview\n    {...props}\n    type=\"right\"\n    style={styles.headerSubview}\n  />\n);\n\nconst ScreenStackHeaderLeftView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => (\n  <ScreensNativeModules.NativeScreenStackHeaderSubview\n    {...props}\n    type=\"left\"\n    style={styles.headerSubview}\n  />\n);\n\nconst ScreenStackHeaderCenterView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => (\n  <ScreensNativeModules.NativeScreenStackHeaderSubview\n    {...props}\n    type=\"center\"\n    style={styles.headerSubview}\n  />\n);\n\nconst ScreenStackHeaderSearchBarView = (\n  props: React.PropsWithChildren<SearchBarProps>\n): JSX.Element => (\n  <ScreensNativeModules.NativeScreenStackHeaderSubview\n    {...props}\n    type=\"searchBar\"\n    style={styles.headerSubview}\n  />\n);\n\nexport type {\n  StackPresentationTypes,\n  StackAnimationTypes,\n  BlurEffectTypes,\n  ScreenReplaceTypes,\n  ScreenOrientationTypes,\n  HeaderSubviewTypes,\n  ScreenProps,\n  ScreenContainerProps,\n  ScreenStackProps,\n  ScreenStackHeaderConfigProps,\n  SearchBarProps,\n};\n\n// context to be used when the user wants to use enhanced implementation\n// e.g. to use `useReanimatedTransitionProgress` (see `reanimated` folder in repo)\nconst ScreenContext = React.createContext(InnerScreen);\n\nclass Screen extends React.Component<ScreenProps> {\n  static contextType = ScreenContext;\n\n  render() {\n    const ScreenWrapper = this.context || InnerScreen;\n    return <ScreenWrapper {...this.props} />;\n  }\n}\n\nmodule.exports = {\n  // these are classes so they are not evaluated until used\n  // so no need to use getters for them\n  Screen,\n  ScreenContainer,\n  ScreenContext,\n  ScreenStack,\n  InnerScreen,\n\n  get NativeScreen() {\n    return ScreensNativeModules.NativeScreen;\n  },\n\n  get NativeScreenContainer() {\n    return ScreensNativeModules.NativeScreenContainer;\n  },\n\n  get NativeScreenNavigationContainer() {\n    return ScreensNativeModules.NativeScreenNavigationContainer;\n  },\n\n  get ScreenStackHeaderConfig() {\n    return ScreensNativeModules.NativeScreenStackHeaderConfig;\n  },\n  get ScreenStackHeaderSubview() {\n    return ScreensNativeModules.NativeScreenStackHeaderSubview;\n  },\n  get SearchBar() {\n    if (!isSearchBarAvailableForCurrentPlatform) {\n      console.warn(\n        'Importing SearchBar is only valid on iOS and Android devices.'\n      );\n      return View;\n    }\n\n    return ScreensNativeModules.NativeSearchBar;\n  },\n  get FullWindowOverlay() {\n    if (Platform.OS !== 'ios') {\n      console.warn('Importing FullWindowOverlay is only valid on iOS devices.');\n      return View;\n    }\n\n    return ScreensNativeModules.NativeFullWindowOverlay;\n  },\n  // these are functions and will not be evaluated until used\n  // so no need to use getters for them\n  ScreenStackHeaderBackButtonImage,\n  ScreenStackHeaderRightView,\n  ScreenStackHeaderLeftView,\n  ScreenStackHeaderCenterView,\n  ScreenStackHeaderSearchBarView,\n\n  enableScreens,\n  enableFreeze,\n  screensEnabled,\n  shouldUseActivityState,\n  useTransitionProgress,\n\n  isSearchBarAvailableForCurrentPlatform,\n  executeNativeBackPress,\n};\n"]}
{"version": 3, "sources": ["createNativeStackNavigator.tsx"], "names": ["React", "Platform", "StyleSheet", "ScreenContext", "ScreenStack", "ScreenStackHeaderBackButtonImage", "ScreenStackHeaderCenterView", "ScreenStackHeaderConfig", "ScreenStackHeaderLeftView", "ScreenStackHeaderRightView", "ScreenStackHeaderSearchBarView", "SearchBar", "createNavigator", "SceneView", "StackActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "REMOVE_ACTION", "isAndroid", "OS", "<PERSON><PERSON><PERSON><PERSON>", "renderComponentOrThunk", "componentOrThunk", "props", "removeScene", "route", "dismissCount", "navigation", "dispatch", "type", "immediate", "key", "onAppear", "descriptor", "options", "completeTransition", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "onFinishTransitioning", "routes", "lastRoute", "length", "renderHeaderConfig", "index", "navigationConfig", "headerMode", "backButtonInCustomView", "direction", "disableBackButtonMenu", "headerBackTitle", "headerBackTitleStyle", "headerBackTitleVisible", "headerHideBackButton", "headerHideShadow", "headerLargeStyle", "headerLargeTitle", "headerLargeTitleHideShadow", "headerLargeTitleStyle", "headerShown", "headerStyle", "headerTintColor", "headerTitleStyle", "headerTopInsetEnabled", "headerTranslucent", "hideShadow", "largeTitle", "largeTitleHideShadow", "title", "translucent", "scene", "headerOptions", "backTitle", "backTitleFontFamily", "fontFamily", "backTitleFontSize", "fontSize", "color", "topInsetEnabled", "hideBackButton", "largeTitleBackgroundColor", "backgroundColor", "largeTitleColor", "largeTitleFontFamily", "largeTitleFontSize", "largeTitleFontWeight", "fontWeight", "titleColor", "titleFontFamily", "titleFontSize", "titleFontWeight", "<PERSON><PERSON><PERSON><PERSON>", "header", "undefined", "blurEffect", "children", "backButtonImage", "push", "searchBar", "headerLeft", "headerBackImage", "goBack", "requestAnimationFrame", "headerPressColorAndroid", "backButtonTitle", "truncatedBackButtonTitle", "backTitleVisible", "layoutPreset", "headerTitle", "headerRight", "MaybeNestedStack", "isHeaderInModal", "screenProps", "SceneComponent", "Screen", "useContext", "styles", "scenes", "absoluteFill", "StackView", "descriptors", "map", "getComponent", "routeNavigationProp", "mode", "transparentCard", "stackPresentation", "card<PERSON>ran<PERSON>arent", "stackAnimation", "animationEnabled", "console", "warn", "isHeaderInPush", "cardStyle", "customAnimationOnSwipe", "replaceAnimation", "gestureEnabled", "nativeBackButtonDismissalEnabled", "fullScreenSwipeEnabled", "screenOrientation", "statusBarAnimation", "statusBarColor", "statusBarHidden", "statusBarStyle", "statusBarTranslucent", "onWillAppear", "onWillDisappear", "onDisappear", "e", "nativeEvent", "create", "flex", "createStackNavigator", "routeConfigMap", "stackConfig", "router", "superGetStateForAction", "getStateForAction", "action", "backRouteIndex", "backRoute", "find", "indexOf", "newRoutes", "splice", "isTransitioning"], "mappings": ";;AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,SACEC,QADF,EAEEC,UAFF,QAOO,cAPP;AAQA,SACEC,aADF,EAEEC,WAFF,EAGEC,gCAHF,EAIEC,2BAJF,EAKEC,uBALF,EAOEC,yBAPF,EAQEC,0BARF,EASEC,8BATF,EAUEC,SAVF,QAYO,sBAZP;AAaA,SACEC,eADF,EAEEC,SAFF,EAGEC,YAHF,EAIEC,WAJF,QAgBO,kBAhBP;AAkBA,SAASC,gBAAT,QAAiC,wBAAjC;AAOA,MAAMC,aAAa,GAAG,6BAAtB;AAEA,MAAMC,SAAS,GAAGjB,QAAQ,CAACkB,EAAT,KAAgB,SAAlC;AAEA,IAAIC,OAAO,GAAGF,SAAd;;AAEA,SAASG,sBAAT,CAAgCC,gBAAhC,EAA2DC,KAA3D,EAA2E;AACzE,MAAI,OAAOD,gBAAP,KAA4B,UAAhC,EAA4C;AAC1C,WAAOA,gBAAgB,CAACC,KAAD,CAAvB;AACD;;AACD,SAAOD,gBAAP;AACD;;AAuED,SAASE,WAAT,CACEC,KADF,EAEEC,YAFF,EAGEC,UAHF,EAIE;AACAA,EAAAA,UAAU,CAACC,QAAX,CAAoB;AAClB;AACAC,IAAAA,IAAI,EAAEZ,aAFY;AAGlBa,IAAAA,SAAS,EAAE,IAHO;AAIlBC,IAAAA,GAAG,EAAEN,KAAK,CAACM,GAJO;AAKlBL,IAAAA;AALkB,GAApB;AAOD;;AAED,SAASM,QAAT,CACEP,KADF,EAEEQ,UAFF,EAGEN,UAHF,EAIE;AAAA;;AACA,yBAAAM,UAAU,CAACC,OAAX,qGAAoBF,QAApB;AACAL,EAAAA,UAAU,CAACC,QAAX,CACEd,YAAY,CAACqB,kBAAb,CAAgC;AAC9BC,IAAAA,UAAU,EAAEX,KAAK,CAACM,GADY;AAE9BA,IAAAA,GAAG,EAAEJ,UAAU,CAACU,KAAX,CAAiBN;AAFQ,GAAhC,CADF;AAMD;;AAED,SAASO,qBAAT,CAA+BX,UAA/B,EAAmE;AACjE,QAAM;AAAEY,IAAAA;AAAF,MAAaZ,UAAU,CAACU,KAA9B;AACA,QAAMG,SAAS,GAAG,CAAAD,MAAM,SAAN,IAAAA,MAAM,WAAN,YAAAA,MAAM,CAAEE,MAAR,KAAkBF,MAAM,CAACA,MAAM,CAACE,MAAP,GAAgB,CAAjB,CAA1C;;AAEA,MAAID,SAAJ,EAAe;AACbb,IAAAA,UAAU,CAACC,QAAX,CACEd,YAAY,CAACqB,kBAAb,CAAgC;AAC9BC,MAAAA,UAAU,EAAEI,SAAS,CAACT,GADQ;AAE9BA,MAAAA,GAAG,EAAEJ,UAAU,CAACU,KAAX,CAAiBN;AAFQ,KAAhC,CADF;AAMD;AACF;;AAED,SAASW,kBAAT,CACEC,KADF,EAEElB,KAFF,EAGEQ,UAHF,EAIEW,gBAJF,EAKE;AACA,QAAM;AAAEV,IAAAA;AAAF,MAAcD,UAApB;AACA,QAAM;AAAEY,IAAAA;AAAF,MAAiBD,gBAAvB;AAEA,QAAM;AACJE,IAAAA,sBADI;AAEJC,IAAAA,SAFI;AAGJC,IAAAA,qBAHI;AAIJC,IAAAA,eAJI;AAKJC,IAAAA,oBALI;AAMJC,IAAAA,sBANI;AAOJC,IAAAA,oBAPI;AAQJC,IAAAA,gBARI;AASJC,IAAAA,gBATI;AAUJC,IAAAA,gBAVI;AAWJC,IAAAA,0BAXI;AAYJC,IAAAA,qBAZI;AAaJC,IAAAA,WAbI;AAcJC,IAAAA,WAdI;AAeJC,IAAAA,eAfI;AAgBJC,IAAAA,gBAhBI;AAiBJC,IAAAA,qBAAqB,GAAG,IAjBpB;AAkBJC,IAAAA,iBAlBI;AAmBJC,IAAAA,UAnBI;AAoBJC,IAAAA,UApBI;AAqBJC,IAAAA,oBArBI;AAsBJC,IAAAA,KAtBI;AAuBJC,IAAAA;AAvBI,MAwBFlC,OAxBJ;AA0BA,QAAMmC,KAAK,GAAG;AACZ1B,IAAAA,KADY;AAEZZ,IAAAA,GAAG,EAAEN,KAAK,CAACM,GAFC;AAGZN,IAAAA,KAHY;AAIZQ,IAAAA;AAJY,GAAd;AAOA,QAAMqC,aAA2C,GAAG;AAClDxB,IAAAA,sBADkD;AAElDyB,IAAAA,SAAS,EAAEpB,sBAAsB,KAAK,KAA3B,GAAmC,EAAnC,GAAwCF,eAFD;AAGlDuB,IAAAA,mBAAmB,EAAEtB,oBAAF,aAAEA,oBAAF,uBAAEA,oBAAoB,CAAEuB,UAHO;AAIlDC,IAAAA,iBAAiB,EAAExB,oBAAF,aAAEA,oBAAF,uBAAEA,oBAAoB,CAAEyB,QAJS;AAKlDC,IAAAA,KAAK,EAAEhB,eAL2C;AAMlDb,IAAAA,SANkD;AAOlDC,IAAAA,qBAPkD;AAQlD6B,IAAAA,eAAe,EAAEf,qBARiC;AASlDgB,IAAAA,cAAc,EAAE1B,oBATkC;AAUlDY,IAAAA,UAAU,EAAEX,gBAAgB,IAAIW,UAVkB;AAWlDC,IAAAA,UAAU,EAAEV,gBAAgB,IAAIU,UAXkB;AAYlDc,IAAAA,yBAAyB,EACvB,CAAAzB,gBAAgB,SAAhB,IAAAA,gBAAgB,WAAhB,YAAAA,gBAAgB,CAAE0B,eAAlB,OACA;AACAvB,IAAAA,qBAFA,aAEAA,qBAFA,uBAEAA,qBAAqB,CAAEuB,eAFvB,CAbgD;AAgBlDC,IAAAA,eAAe,EAAExB,qBAAF,aAAEA,qBAAF,uBAAEA,qBAAqB,CAAEmB,KAhBU;AAiBlDM,IAAAA,oBAAoB,EAAEzB,qBAAF,aAAEA,qBAAF,uBAAEA,qBAAqB,CAAEgB,UAjBK;AAkBlDU,IAAAA,kBAAkB,EAAE1B,qBAAF,aAAEA,qBAAF,uBAAEA,qBAAqB,CAAEkB,QAlBO;AAmBlDS,IAAAA,oBAAoB,EAAE3B,qBAAF,aAAEA,qBAAF,uBAAEA,qBAAqB,CAAE4B,UAnBK;AAoBlDnB,IAAAA,oBAAoB,EAAEA,oBAAoB,IAAIV,0BApBI;AAqBlDW,IAAAA,KArBkD;AAsBlDmB,IAAAA,UAAU,EAAE,CAAAzB,gBAAgB,SAAhB,IAAAA,gBAAgB,WAAhB,YAAAA,gBAAgB,CAAEe,KAAlB,KAA2BhB,eAtBW;AAuBlD2B,IAAAA,eAAe,EAAE1B,gBAAF,aAAEA,gBAAF,uBAAEA,gBAAgB,CAAEY,UAvBe;AAwBlDe,IAAAA,aAAa,EAAE3B,gBAAF,aAAEA,gBAAF,uBAAEA,gBAAgB,CAAEc,QAxBiB;AAyBlDc,IAAAA,eAAe,EAAE5B,gBAAF,aAAEA,gBAAF,uBAAEA,gBAAgB,CAAEwB,UAzBe;AA0BlDjB,IAAAA,WAAW,EAAEL,iBAAiB,IAAIK,WAArB,IAAoC;AA1BC,GAApD;AA6BA,QAAMsB,SAAS,GACbhC,WAAW,KAAK,KAAhB,IAAyBb,UAAU,KAAK,MAAxC,IAAkDX,OAAO,CAACyD,MAAR,KAAmB,IADvE;;AAEA,MAAI,CAACD,SAAL,EAAgB;AACd,wBAAO,oBAAC,uBAAD,eAA6BpB,aAA7B;AAA4C,MAAA,MAAM;AAAlD,OAAP;AACD;;AAED,MAAIX,WAAW,KAAKiC,SAApB,EAA+B;AAC7BtB,IAAAA,aAAa,CAACU,eAAd,GAAgCrB,WAAW,CAACqB,eAA5C;AACAV,IAAAA,aAAa,CAACuB,UAAd,GAA2BlC,WAAW,CAACkC,UAAvC;AACD;;AAED,QAAMC,QAAQ,GAAG,EAAjB;;AAEA,MAAI5D,OAAO,CAAC6D,eAAZ,EAA6B;AAC3BD,IAAAA,QAAQ,CAACE,IAAT,eACE,oBAAC,gCAAD;AACE,MAAA,GAAG,EAAC,WADN;AAEE,MAAA,MAAM,EAAE9D,OAAO,CAAC6D;AAFlB,MADF;AAMD;;AAED,MAAI9F,QAAQ,CAACkB,EAAT,KAAgB,KAAhB,IAAyBe,OAAO,CAAC+D,SAArC,EAAgD;AAC9CH,IAAAA,QAAQ,CAACE,IAAT,eACE,oBAAC,8BAAD,qBACE,oBAAC,SAAD,EAAe9D,OAAO,CAAC+D,SAAvB,CADF,CADF;AAKD;;AAED,MAAI/D,OAAO,CAACgE,UAAR,KAAuBN,SAA3B,EAAsC;AACpCE,IAAAA,QAAQ,CAACE,IAAT,eACE,oBAAC,yBAAD;AAA2B,MAAA,GAAG,EAAC;AAA/B,OACG3E,sBAAsB,CAACa,OAAO,CAACgE,UAAT,EAAqB;AAAE7B,MAAAA;AAAF,KAArB,CADzB,CADF;AAKD,GAND,MAMO,IAAInC,OAAO,CAACiE,eAAR,KAA4BP,SAAhC,EAA2C;AAChD,UAAMQ,MAAM,GAAG,MAAM;AACnB;AACAC,MAAAA,qBAAqB,CAAC,MAAM;AAC1BpE,QAAAA,UAAU,CAACN,UAAX,CAAsByE,MAAtB,CAA6BnE,UAAU,CAACF,GAAxC;AACD,OAFoB,CAArB;AAGD,KALD;;AAOA+D,IAAAA,QAAQ,CAACE,IAAT,eACE,oBAAC,yBAAD;AAA2B,MAAA,GAAG,EAAC;AAA/B,oBACE,oBAAC,gBAAD;AACE,MAAA,OAAO,EAAEI,MADX;AAEE,MAAA,iBAAiB,EAAElE,OAAO,CAACoE,uBAF7B;AAGE,MAAA,SAAS,EAAEpE,OAAO,CAAC0B,eAHrB;AAIE,MAAA,SAAS,EAAE1B,OAAO,CAACiE,eAJrB;AAKE,MAAA,KAAK,EAAEjE,OAAO,CAACqE,eALjB;AAME,MAAA,cAAc,EAAErE,OAAO,CAACsE,wBAN1B;AAOE,MAAA,YAAY,EAAEtE,OAAO,CAACuE,gBAPxB;AAQE,MAAA,UAAU,EAAEvE,OAAO,CAACgB,oBARtB;AASE,MAAA,WAAW,EAAEhB,OAAO,CAACwE,YATvB,CAUE;AAVF;AAWE,MAAA,KAAK,EAAExE,OAAO,CAACqE,eAXjB;AAYE,MAAA,cAAc,EAAErE,OAAO,CAACsE,wBAZ1B;AAaE,MAAA,gBAAgB,EAAEtE,OAAO,CAACuE,gBAb5B;AAcE,MAAA,UAAU,EAAEvE,OAAO,CAACgB,oBAdtB;AAeE,MAAA,YAAY,EAAEhB,OAAO,CAACwE,YAfxB;AAgBE,MAAA,KAAK,EAAErC;AAhBT,MADF,CADF;AAsBD;;AAED,MAAInC,OAAO,CAACyE,WAAZ,EAAyB;AACvB,QAAIxC,KAAK,KAAKyB,SAAV,IAAuB,OAAO1D,OAAO,CAACyE,WAAf,KAA+B,QAA1D,EAAoE;AAClErC,MAAAA,aAAa,CAACH,KAAd,GAAsBjC,OAAO,CAACyE,WAA9B;AACD,KAFD,MAEO;AACLb,MAAAA,QAAQ,CAACE,IAAT,eACE,oBAAC,2BAAD;AAA6B,QAAA,GAAG,EAAC;AAAjC,SACG3E,sBAAsB,CAACa,OAAO,CAACyE,WAAT,EAAsB;AAAEtC,QAAAA;AAAF,OAAtB,CADzB,CADF;AAKD;AACF;;AAED,MAAInC,OAAO,CAAC0E,WAAZ,EAAyB;AACvBd,IAAAA,QAAQ,CAACE,IAAT,eACE,oBAAC,0BAAD;AAA4B,MAAA,GAAG,EAAC;AAAhC,OACG3E,sBAAsB,CAACa,OAAO,CAAC0E,WAAT,EAAsB;AAAEvC,MAAAA;AAAF,KAAtB,CADzB,CADF;AAKD;;AAED,MAAIyB,QAAQ,CAACrD,MAAT,GAAkB,CAAtB,EAAyB;AACvB6B,IAAAA,aAAa,CAACwB,QAAd,GAAyBA,QAAzB;AACD;;AAED,sBAAO,oBAAC,uBAAD,EAA6BxB,aAA7B,CAAP;AACD;;AAED,MAAMuC,gBAAgB,GAAG,CAAC;AACxBC,EAAAA,eADwB;AAExBC,EAAAA,WAFwB;AAGxBtF,EAAAA,KAHwB;AAIxBE,EAAAA,UAJwB;AAKxBqF,EAAAA,cALwB;AAMxBrE,EAAAA,KANwB;AAOxBV,EAAAA,UAPwB;AAQxBW,EAAAA;AARwB,CAAD,KAqBnB;AACJ,QAAMqE,MAAM,GAAGjH,KAAK,CAACkH,UAAN,CAAiB/G,aAAjB,CAAf;;AAEA,MAAI2G,eAAJ,EAAqB;AACnB,wBACE,oBAAC,WAAD;AAAa,MAAA,KAAK,EAAEK,MAAM,CAACC;AAA3B,oBACE,oBAAC,MAAD;AAAQ,MAAA,KAAK,EAAElH,UAAU,CAACmH,YAA1B;AAAwC,MAAA,OAAO,MAA/C;AAAgD,MAAA,aAAa;AAA7D,OACG3E,kBAAkB,CAACC,KAAD,EAAQlB,KAAR,EAAeQ,UAAf,EAA2BW,gBAA3B,CADrB,eAEE,oBAAC,SAAD;AACE,MAAA,WAAW,EAAEmE,WADf;AAEE,MAAA,UAAU,EAAEpF,UAFd;AAGE,MAAA,SAAS,EAAEqF;AAHb,MAFF,CADF,CADF;AAYD;;AACD,sBACE,oBAAC,SAAD;AACE,IAAA,WAAW,EAAED,WADf;AAEE,IAAA,UAAU,EAAEpF,UAFd;AAGE,IAAA,SAAS,EAAEqF;AAHb,IADF;AAOD,CA7CD;;AAsDA,SAASM,SAAT,CAAmB;AACjB3F,EAAAA,UADiB;AAEjB4F,EAAAA,WAFiB;AAGjB3E,EAAAA,gBAHiB;AAIjBmE,EAAAA;AAJiB,CAAnB,EAKmB;AACjB,QAAM;AAAExE,IAAAA;AAAF,MAAaZ,UAAU,CAACU,KAA9B;AACA,QAAM4E,MAAM,GAAGjH,KAAK,CAACkH,UAAN,CAAiB/G,aAAjB,CAAf;AACA,sBACE,oBAAC,WAAD;AACE,IAAA,KAAK,EAAEgH,MAAM,CAACC,MADhB;AAEE,IAAA,qBAAqB,EAAE,MAAM9E,qBAAqB,CAACX,UAAD;AAFpD,KAGGY,MAAM,CAACiF,GAAP,CAAW,CAAC/F,KAAD,EAAQkB,KAAR,KAAkB;AAC5B,UAAMV,UAAU,GAAGsF,WAAW,CAAC9F,KAAK,CAACM,GAAP,CAA9B;AACA,UAAM;AAAE0F,MAAAA,YAAF;AAAgBvF,MAAAA;AAAhB,QAA4BD,UAAlC;AACA,UAAMyF,mBAAmB,GAAGzF,UAAU,CAACN,UAAvC;AACA,UAAM;AAAEgG,MAAAA,IAAF;AAAQC,MAAAA;AAAR,QAA4BhF,gBAAlC;AACA,UAAMoE,cAAc,GAAGS,YAAY,EAAnC;AAEA,QAAII,iBAAyC,GAAG,MAAhD;;AAEA,QAAI3F,OAAO,CAAC2F,iBAAZ,EAA+B;AAC7BA,MAAAA,iBAAiB,GAAG3F,OAAO,CAAC2F,iBAA5B;AACD,KAFD,MAEO;AACL;AACA,UAAIF,IAAI,KAAK,OAAT,IAAoBA,IAAI,KAAK,gBAAjC,EAAmD;AACjDE,QAAAA,iBAAiB,GAAGF,IAApB;;AACA,YAAIC,eAAe,IAAI1F,OAAO,CAAC4F,eAA/B,EAAgD;AAC9CD,UAAAA,iBAAiB,GACfF,IAAI,KAAK,gBAAT,GACI,2BADJ,GAEI,kBAHN;AAID;AACF;AACF;;AACD,QAAII,cAAc,GAAG7F,OAAO,CAAC6F,cAA7B;;AACA,QAAI7F,OAAO,CAAC8F,gBAAR,KAA6B,KAAjC,EAAwC;AACtCD,MAAAA,cAAc,GAAG,MAAjB;AACD;;AAED,UAAMrC,SAAS,GACbxD,OAAO,CAACwB,WAAR,KAAwB,KAAxB,IACA,CAAAd,gBAAgB,SAAhB,IAAAA,gBAAgB,WAAhB,YAAAA,gBAAgB,CAAEC,UAAlB,MAAiC,MADjC,IAEAX,OAAO,CAACyD,MAAR,KAAmB,IAHrB;;AAKA,QACE,CAACvE,OAAD,IACAyG,iBAAiB,KAAK,MADtB,IAEA3F,OAAO,CAACwB,WAAR,KAAwBkC,SAH1B,EAIE;AACAxE,MAAAA,OAAO,GAAG,IAAV;AACA6G,MAAAA,OAAO,CAACC,IAAR,CACE,mHADF;AAGD;;AAED,UAAMpB,eAAe,GAAG5F,SAAS,GAC7B,KAD6B,GAE7B2G,iBAAiB,KAAK,MAAtB,IACAnC,SADA,IAEAxD,OAAO,CAACwB,WAAR,KAAwB,IAJ5B;AAKA,UAAMyE,cAAc,GAAGjH,SAAS,GAC5BwE,SAD4B,GAE5BmC,iBAAiB,KAAK,MAAtB,IAAgCnC,SAFpC;AAIA,wBACE,oBAAC,MAAD;AACE,MAAA,GAAG,EAAG,UAASjE,KAAK,CAACM,GAAI,EAD3B;AAEE,MAAA,OAAO,MAFT;AAGE,MAAA,aAAa,MAHf;AAIE,MAAA,KAAK,EAAE,CAAC7B,UAAU,CAACmH,YAAZ,EAA0BnF,OAAO,CAACkG,SAAlC,CAJT;AAKE,MAAA,cAAc,EAAEL,cALlB;AAME,MAAA,sBAAsB,EAAE7F,OAAO,CAACmG,sBANlC;AAOE,MAAA,iBAAiB,EAAER,iBAPrB;AAQE,MAAA,gBAAgB,EACd3F,OAAO,CAACoG,gBAAR,KAA6B1C,SAA7B,GACI,KADJ,GAEI1D,OAAO,CAACoG,gBAXhB;AAaE,MAAA,aAAa,EACX3F,KAAK,KAAKhB,UAAU,CAACU,KAAX,CAAiBE,MAAjB,CAAwBE,MAAxB,GAAiC,CAA3C,GAA+C,MAA/C,GAAwD,MAd5D;AAgBE,MAAA,cAAc,EACZxC,QAAQ,CAACkB,EAAT,KAAgB,SAAhB,GACI,KADJ,GAEIe,OAAO,CAACqG,cAAR,KAA2B3C,SAA3B,GACA,IADA,GAEA1D,OAAO,CAACqG,cArBhB;AAuBE,MAAA,gCAAgC,EAC9BrG,OAAO,CAACsG,gCAxBZ;AA0BE,MAAA,sBAAsB,EAAEtG,OAAO,CAACuG,sBA1BlC;AA2BE,MAAA,iBAAiB,EAAEvG,OAAO,CAACwG,iBA3B7B;AA4BE,MAAA,kBAAkB,EAAExG,OAAO,CAACyG,kBA5B9B;AA6BE,MAAA,cAAc,EAAEzG,OAAO,CAAC0G,cA7B1B;AA8BE,MAAA,eAAe,EAAE1G,OAAO,CAAC2G,eA9B3B;AA+BE,MAAA,cAAc,EAAE3G,OAAO,CAAC4G,cA/B1B;AAgCE,MAAA,oBAAoB,EAAE5G,OAAO,CAAC6G,oBAhChC;AAiCE,MAAA,QAAQ,EAAE,MAAM/G,QAAQ,CAACP,KAAD,EAAQQ,UAAR,EAAoByF,mBAApB,CAjC1B;AAkCE,MAAA,YAAY,EAAE;AAAA;;AAAA,eAAMxF,OAAN,aAAMA,OAAN,gDAAMA,OAAO,CAAE8G,YAAf,0DAAM,2BAAA9G,OAAO,CAAb;AAAA,OAlChB;AAmCE,MAAA,eAAe,EAAE;AAAA;;AAAA,eAAMA,OAAN,aAAMA,OAAN,gDAAMA,OAAO,CAAE+G,eAAf,0DAAM,2BAAA/G,OAAO,CAAb;AAAA,OAnCnB;AAoCE,MAAA,WAAW,EAAE;AAAA;;AAAA,eAAMA,OAAN,aAAMA,OAAN,+CAAMA,OAAO,CAAEgH,WAAf,yDAAM,0BAAAhH,OAAO,CAAb;AAAA,OApCf;AAqCE,MAAA,yBAAyB,EAAE,MACzBV,WAAW,CAACC,KAAD,EAAQ,CAAR,EAAWiG,mBAAX,CAtCf;AAwCE,MAAA,WAAW,EAAGyB,CAAD,IACX3H,WAAW,CACTC,KADS,EAET0H,CAAC,CAACC,WAAF,CAAc1H,YAFL,EAGTgG,mBAHS;AAzCf,OA+CGS,cAAc,IACbzF,kBAAkB,CAACC,KAAD,EAAQlB,KAAR,EAAeQ,UAAf,EAA2BW,gBAA3B,CAhDtB,eAiDE,oBAAC,gBAAD;AACE,MAAA,eAAe,EAAEkE,eADnB;AAEE,MAAA,WAAW,EAAEC,WAFf;AAGE,MAAA,KAAK,EAAEtF,KAHT;AAIE,MAAA,UAAU,EAAEiG,mBAJd;AAKE,MAAA,cAAc,EAAEV,cALlB;AAME,MAAA,KAAK,EAAErE,KANT;AAOE,MAAA,UAAU,EAAEV,UAPd;AAQE,MAAA,gBAAgB,EAAEW;AARpB,MAjDF,CADF;AA8DD,GAnHA,CAHH,CADF;AA0HD;;AAED,MAAMuE,MAAM,GAAGjH,UAAU,CAACmJ,MAAX,CAAkB;AAC/BjC,EAAAA,MAAM,EAAE;AAAEkC,IAAAA,IAAI,EAAE;AAAR;AADuB,CAAlB,CAAf;;AAIA,SAASC,oBAAT,CACEC,cADF,EAKEC,WAKC,GAAG,EAVN,EAcE;AACA,QAAMC,MAAM,GAAG3I,WAAW,CAACyI,cAAD,EAAiBC,WAAjB,CAA1B,CADA,CAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,QAAME,sBAAsB,GAAGD,MAAM,CAACE,iBAAtC;;AACAF,EAAAA,MAAM,CAACE,iBAAP,GAA2B,CACzBC,MADyB,EAEzBxH,KAFyB,KAGtB;AACH,QAAIwH,MAAM,CAAChI,IAAP,KAAgBZ,aAApB,EAAmC;AACjC,YAAM;AAAEc,QAAAA,GAAF;AAAOD,QAAAA,SAAP;AAAkBJ,QAAAA;AAAlB,UAAmCmI,MAAzC;AACA,UAAIC,cAAc,GAAGzH,KAAK,CAACM,KAA3B;;AACA,UAAIZ,GAAJ,EAAS;AACP,cAAMgI,SAAS,GAAG1H,KAAK,CAACE,MAAN,CAAayH,IAAb,CACfvI,KAAD,IAA8CA,KAAK,CAACM,GAAN,KAAcA,GAD5C,CAAlB;AAGA+H,QAAAA,cAAc,GAAGzH,KAAK,CAACE,MAAN,CAAa0H,OAAb,CAAqBF,SAArB,CAAjB;AACD;;AAED,UAAID,cAAc,GAAG,CAArB,EAAwB;AACtB,cAAMI,SAAS,GAAG,CAAC,GAAG7H,KAAK,CAACE,MAAV,CAAlB;;AACA,YAAIb,YAAY,GAAG,CAAnB,EAAsB;AACpB;AACA;AACA;AACAwI,UAAAA,SAAS,CAACC,MAAV,CAAiBL,cAAc,GAAGpI,YAAjB,GAAgC,CAAjD,EAAoDA,YAApD;AACD,SALD,MAKO;AACLwI,UAAAA,SAAS,CAACC,MAAV,CAAiBL,cAAjB,EAAiC,CAAjC;AACD;;AAED,eAAO,EACL,GAAGzH,KADE;AAELE,UAAAA,MAAM,EAAE2H,SAFH;AAGLvH,UAAAA,KAAK,EAAEuH,SAAS,CAACzH,MAAV,GAAmB,CAHrB;AAIL2H,UAAAA,eAAe,EAAEtI,SAAS,KAAK;AAJ1B,SAAP;AAMD;AACF;;AACD,WAAO6H,sBAAsB,CAACE,MAAD,EAA6BxH,KAA7B,CAA7B;AACD,GAlCD,CAXA,CA8CA;;;AACA,SAAOzB,eAAe,CAAC0G,SAAD,EAAYoC,MAAZ,EAAoBD,WAApB,CAAtB;AACD;;AAED,eAAeF,oBAAf", "sourcesContent": ["import React from 'react';\nimport {\n  Platform,\n  StyleSheet,\n  Animated,\n  StyleProp,\n  TextStyle,\n  ViewStyle,\n} from 'react-native';\nimport {\n  ScreenContext,\n  ScreenStack,\n  ScreenStackHeaderBackButtonImage,\n  ScreenStackHeaderCenterView,\n  ScreenStackHeaderConfig,\n  ScreenStackHeaderConfigProps,\n  ScreenStackHeaderLeftView,\n  ScreenStackHeaderRightView,\n  ScreenStackHeaderSearchBarView,\n  SearchBar,\n  StackPresentationTypes,\n} from 'react-native-screens';\nimport {\n  createNavigator,\n  SceneView,\n  StackActions,\n  StackRouter,\n  NavigationRouteConfigMap,\n  CreateNavigatorConfig,\n  NavigationStackRouterConfig,\n  NavigationParams,\n  NavigationRoute,\n  NavigationDescriptor,\n  NavigationState,\n  NavigationNavigator,\n  NavigationAction,\n  NavigationProp,\n  NavigationScreenProp,\n} from 'react-navigation';\nimport { NativeStackNavigationOptions as NativeStackNavigationOptionsV5 } from './native-stack/types';\nimport { HeaderBackButton } from 'react-navigation-stack';\nimport {\n  StackNavigationHelpers,\n  StackNavigationProp,\n  Layout,\n} from 'react-navigation-stack/src/vendor/types';\n\nconst REMOVE_ACTION = 'NativeStackNavigator/REMOVE';\n\nconst isAndroid = Platform.OS === 'android';\n\nlet didWarn = isAndroid;\n\nfunction renderComponentOrThunk(componentOrThunk: unknown, props: unknown) {\n  if (typeof componentOrThunk === 'function') {\n    return componentOrThunk(props);\n  }\n  return componentOrThunk;\n}\n\ntype NativeStackRemoveNavigationAction = {\n  type: typeof REMOVE_ACTION;\n  immediate: boolean;\n  dismissCount: number;\n  key?: string;\n};\n\nexport type NativeStackNavigationProp = StackNavigationProp;\n\nexport type NativeStackNavigationOptions = StackNavigatorOptions &\n  NativeStackNavigationOptionsV5 &\n  BackButtonProps & {\n    onWillAppear?: () => void;\n    onAppear?: () => void;\n    onWillDisappear?: () => void;\n    onDisappear?: () => void;\n    // these props differ from the ones used in v5 `native-stack`, and we would like to keep the API consistent between versions\n    /** Use `headerHideShadow` to be consistent with v5 `native-stack` */\n    hideShadow?: boolean;\n    /** Use `headerLargeTitle` to be consistent with v5 `native-stack` */\n    largeTitle?: boolean;\n    /** Use `headerLargeTitleHideShadow` to be consistent with v5 `native-stack` */\n    largeTitleHideShadow?: boolean;\n    /** Use `headerTranslucent` to be consistent with v5 `native-stack` */\n    translucent?: boolean;\n  };\n\n// these are adopted from `stack` navigator\ntype StackNavigatorOptions = {\n  /** This is an option from `stackNavigator` and it hides the header when set to `null`. Use `headerShown` instead to be consistent with v5 `native-stack`. */\n  header?: React.ComponentType<Record<string, unknown>> | null;\n  /** This is an option from `stackNavigator` and it controls the stack presentation along with `mode` prop. Use `stackPresentation` instead to be consistent with v5 `native-stack` */\n  cardTransparent?: boolean;\n  /** This is an option from `stackNavigator` and it sets stack animation to none when `false` passed. Use `stackAnimation: 'none'` instead to be consistent with v5 `native-stack` */\n  animationEnabled?: boolean;\n  cardStyle?: StyleProp<ViewStyle>;\n};\n\n// these are the props used for rendering back button taken from `react-navigation-stack`\ntype BackButtonProps = {\n  headerBackImage?: (props: { tintColor: string }) => React.ReactNode;\n  headerPressColorAndroid?: string;\n  headerTintColor?: string;\n  backButtonTitle?: string;\n  truncatedBackButtonTitle?: string;\n  backTitleVisible?: boolean;\n  headerBackTitleStyle?: Animated.WithAnimatedValue<StyleProp<TextStyle>>;\n  layoutPreset?: Layout;\n};\n\ntype NativeStackDescriptor = NavigationDescriptor<\n  NavigationParams,\n  NativeStackNavigationOptions\n>;\n\ntype NativeStackDescriptorMap = {\n  [key: string]: NativeStackDescriptor;\n};\n\n// these are the props used for rendering back button taken from `react-navigation-stack`\ntype NativeStackNavigationConfig = {\n  /** This is an option from `stackNavigator` and controls the stack presentation along with `cardTransparent` prop. Use `stackPresentation` instead to be consistent with v5 `native-stack` */\n  mode?: 'modal' | 'containedModal';\n  /** This is an option from `stackNavigator` and makes the header hide when set to `none`. Use `headerShown` instead to be consistent with v5 `native-stack` */\n  headerMode?: 'none';\n  /** This is an option from `stackNavigator` and controls the stack presentation along with `mode` prop. Use `stackPresentation` instead to be consistent with v5 `native-stack` */\n  transparentCard?: boolean;\n};\n\nfunction removeScene(\n  route: NavigationRoute<NavigationParams>,\n  dismissCount: number,\n  navigation: StackNavigationHelpers\n) {\n  navigation.dispatch({\n    // @ts-ignore special navigation action for native stack\n    type: REMOVE_ACTION,\n    immediate: true,\n    key: route.key,\n    dismissCount,\n  });\n}\n\nfunction onAppear(\n  route: NavigationRoute<NavigationParams>,\n  descriptor: NativeStackDescriptor,\n  navigation: StackNavigationHelpers\n) {\n  descriptor.options?.onAppear?.();\n  navigation.dispatch(\n    StackActions.completeTransition({\n      toChildKey: route.key,\n      key: navigation.state.key,\n    })\n  );\n}\n\nfunction onFinishTransitioning(navigation: StackNavigationHelpers) {\n  const { routes } = navigation.state;\n  const lastRoute = routes?.length && routes[routes.length - 1];\n\n  if (lastRoute) {\n    navigation.dispatch(\n      StackActions.completeTransition({\n        toChildKey: lastRoute.key,\n        key: navigation.state.key,\n      })\n    );\n  }\n}\n\nfunction renderHeaderConfig(\n  index: number,\n  route: NavigationRoute<NavigationParams>,\n  descriptor: NativeStackDescriptor,\n  navigationConfig: NativeStackNavigationConfig\n) {\n  const { options } = descriptor;\n  const { headerMode } = navigationConfig;\n\n  const {\n    backButtonInCustomView,\n    direction,\n    disableBackButtonMenu,\n    headerBackTitle,\n    headerBackTitleStyle,\n    headerBackTitleVisible,\n    headerHideBackButton,\n    headerHideShadow,\n    headerLargeStyle,\n    headerLargeTitle,\n    headerLargeTitleHideShadow,\n    headerLargeTitleStyle,\n    headerShown,\n    headerStyle,\n    headerTintColor,\n    headerTitleStyle,\n    headerTopInsetEnabled = true,\n    headerTranslucent,\n    hideShadow,\n    largeTitle,\n    largeTitleHideShadow,\n    title,\n    translucent,\n  } = options;\n\n  const scene = {\n    index,\n    key: route.key,\n    route,\n    descriptor,\n  };\n\n  const headerOptions: ScreenStackHeaderConfigProps = {\n    backButtonInCustomView,\n    backTitle: headerBackTitleVisible === false ? '' : headerBackTitle,\n    backTitleFontFamily: headerBackTitleStyle?.fontFamily,\n    backTitleFontSize: headerBackTitleStyle?.fontSize,\n    color: headerTintColor,\n    direction,\n    disableBackButtonMenu,\n    topInsetEnabled: headerTopInsetEnabled,\n    hideBackButton: headerHideBackButton,\n    hideShadow: headerHideShadow || hideShadow,\n    largeTitle: headerLargeTitle || largeTitle,\n    largeTitleBackgroundColor:\n      headerLargeStyle?.backgroundColor ||\n      // @ts-ignore old implementation, will not be present in TS API, but can be used here\n      headerLargeTitleStyle?.backgroundColor,\n    largeTitleColor: headerLargeTitleStyle?.color,\n    largeTitleFontFamily: headerLargeTitleStyle?.fontFamily,\n    largeTitleFontSize: headerLargeTitleStyle?.fontSize,\n    largeTitleFontWeight: headerLargeTitleStyle?.fontWeight,\n    largeTitleHideShadow: largeTitleHideShadow || headerLargeTitleHideShadow,\n    title,\n    titleColor: headerTitleStyle?.color || headerTintColor,\n    titleFontFamily: headerTitleStyle?.fontFamily,\n    titleFontSize: headerTitleStyle?.fontSize,\n    titleFontWeight: headerTitleStyle?.fontWeight,\n    translucent: headerTranslucent || translucent || false,\n  };\n\n  const hasHeader =\n    headerShown !== false && headerMode !== 'none' && options.header !== null;\n  if (!hasHeader) {\n    return <ScreenStackHeaderConfig {...headerOptions} hidden />;\n  }\n\n  if (headerStyle !== undefined) {\n    headerOptions.backgroundColor = headerStyle.backgroundColor;\n    headerOptions.blurEffect = headerStyle.blurEffect;\n  }\n\n  const children = [];\n\n  if (options.backButtonImage) {\n    children.push(\n      <ScreenStackHeaderBackButtonImage\n        key=\"backImage\"\n        source={options.backButtonImage}\n      />\n    );\n  }\n\n  if (Platform.OS === 'ios' && options.searchBar) {\n    children.push(\n      <ScreenStackHeaderSearchBarView>\n        <SearchBar {...options.searchBar} />\n      </ScreenStackHeaderSearchBarView>\n    );\n  }\n\n  if (options.headerLeft !== undefined) {\n    children.push(\n      <ScreenStackHeaderLeftView key=\"left\">\n        {renderComponentOrThunk(options.headerLeft, { scene })}\n      </ScreenStackHeaderLeftView>\n    );\n  } else if (options.headerBackImage !== undefined) {\n    const goBack = () => {\n      // Go back on next tick because button ripple effect needs to happen on Android\n      requestAnimationFrame(() => {\n        descriptor.navigation.goBack(descriptor.key);\n      });\n    };\n\n    children.push(\n      <ScreenStackHeaderLeftView key=\"left\">\n        <HeaderBackButton\n          onPress={goBack}\n          pressColorAndroid={options.headerPressColorAndroid}\n          tintColor={options.headerTintColor}\n          backImage={options.headerBackImage}\n          label={options.backButtonTitle}\n          truncatedLabel={options.truncatedBackButtonTitle}\n          labelVisible={options.backTitleVisible}\n          labelStyle={options.headerBackTitleStyle}\n          titleLayout={options.layoutPreset}\n          // @ts-ignore old props kept for very old version of `react-navigation-stack`\n          title={options.backButtonTitle}\n          truncatedTitle={options.truncatedBackButtonTitle}\n          backTitleVisible={options.backTitleVisible}\n          titleStyle={options.headerBackTitleStyle}\n          layoutPreset={options.layoutPreset}\n          scene={scene}\n        />\n      </ScreenStackHeaderLeftView>\n    );\n  }\n\n  if (options.headerTitle) {\n    if (title === undefined && typeof options.headerTitle === 'string') {\n      headerOptions.title = options.headerTitle;\n    } else {\n      children.push(\n        <ScreenStackHeaderCenterView key=\"center\">\n          {renderComponentOrThunk(options.headerTitle, { scene })}\n        </ScreenStackHeaderCenterView>\n      );\n    }\n  }\n\n  if (options.headerRight) {\n    children.push(\n      <ScreenStackHeaderRightView key=\"right\">\n        {renderComponentOrThunk(options.headerRight, { scene })}\n      </ScreenStackHeaderRightView>\n    );\n  }\n\n  if (children.length > 0) {\n    headerOptions.children = children;\n  }\n\n  return <ScreenStackHeaderConfig {...headerOptions} />;\n}\n\nconst MaybeNestedStack = ({\n  isHeaderInModal,\n  screenProps,\n  route,\n  navigation,\n  SceneComponent,\n  index,\n  descriptor,\n  navigationConfig,\n}: {\n  isHeaderInModal: boolean;\n  screenProps: unknown;\n  route: NavigationRoute<NavigationParams>;\n  navigation: NavigationScreenProp<\n    NavigationRoute<NavigationParams>,\n    NavigationParams\n  >;\n  SceneComponent: React.ComponentType<Record<string, unknown>>;\n  index: number;\n  descriptor: NativeStackDescriptor;\n  navigationConfig: NativeStackNavigationConfig;\n}) => {\n  const Screen = React.useContext(ScreenContext);\n\n  if (isHeaderInModal) {\n    return (\n      <ScreenStack style={styles.scenes}>\n        <Screen style={StyleSheet.absoluteFill} enabled isNativeStack>\n          {renderHeaderConfig(index, route, descriptor, navigationConfig)}\n          <SceneView\n            screenProps={screenProps}\n            navigation={navigation}\n            component={SceneComponent}\n          />\n        </Screen>\n      </ScreenStack>\n    );\n  }\n  return (\n    <SceneView\n      screenProps={screenProps}\n      navigation={navigation}\n      component={SceneComponent}\n    />\n  );\n};\n\ntype StackViewProps = {\n  navigation: StackNavigationHelpers;\n  descriptors: NativeStackDescriptorMap;\n  navigationConfig: NativeStackNavigationConfig;\n  screenProps: unknown;\n};\n\nfunction StackView({\n  navigation,\n  descriptors,\n  navigationConfig,\n  screenProps,\n}: StackViewProps) {\n  const { routes } = navigation.state;\n  const Screen = React.useContext(ScreenContext);\n  return (\n    <ScreenStack\n      style={styles.scenes}\n      onFinishTransitioning={() => onFinishTransitioning(navigation)}>\n      {routes.map((route, index) => {\n        const descriptor = descriptors[route.key];\n        const { getComponent, options } = descriptor;\n        const routeNavigationProp = descriptor.navigation;\n        const { mode, transparentCard } = navigationConfig;\n        const SceneComponent = getComponent();\n\n        let stackPresentation: StackPresentationTypes = 'push';\n\n        if (options.stackPresentation) {\n          stackPresentation = options.stackPresentation;\n        } else {\n          // this shouldn't be used because we have a prop for that\n          if (mode === 'modal' || mode === 'containedModal') {\n            stackPresentation = mode;\n            if (transparentCard || options.cardTransparent) {\n              stackPresentation =\n                mode === 'containedModal'\n                  ? 'containedTransparentModal'\n                  : 'transparentModal';\n            }\n          }\n        }\n        let stackAnimation = options.stackAnimation;\n        if (options.animationEnabled === false) {\n          stackAnimation = 'none';\n        }\n\n        const hasHeader =\n          options.headerShown !== false &&\n          navigationConfig?.headerMode !== 'none' &&\n          options.header !== null;\n\n        if (\n          !didWarn &&\n          stackPresentation !== 'push' &&\n          options.headerShown !== undefined\n        ) {\n          didWarn = true;\n          console.warn(\n            'Be aware that changing the visibility of header in modal on iOS will result in resetting the state of the screen.'\n          );\n        }\n\n        const isHeaderInModal = isAndroid\n          ? false\n          : stackPresentation !== 'push' &&\n            hasHeader &&\n            options.headerShown === true;\n        const isHeaderInPush = isAndroid\n          ? hasHeader\n          : stackPresentation === 'push' && hasHeader;\n\n        return (\n          <Screen\n            key={`screen_${route.key}`}\n            enabled\n            isNativeStack\n            style={[StyleSheet.absoluteFill, options.cardStyle]}\n            stackAnimation={stackAnimation}\n            customAnimationOnSwipe={options.customAnimationOnSwipe}\n            stackPresentation={stackPresentation}\n            replaceAnimation={\n              options.replaceAnimation === undefined\n                ? 'pop'\n                : options.replaceAnimation\n            }\n            pointerEvents={\n              index === navigation.state.routes.length - 1 ? 'auto' : 'none'\n            }\n            gestureEnabled={\n              Platform.OS === 'android'\n                ? false\n                : options.gestureEnabled === undefined\n                ? true\n                : options.gestureEnabled\n            }\n            nativeBackButtonDismissalEnabled={\n              options.nativeBackButtonDismissalEnabled\n            }\n            fullScreenSwipeEnabled={options.fullScreenSwipeEnabled}\n            screenOrientation={options.screenOrientation}\n            statusBarAnimation={options.statusBarAnimation}\n            statusBarColor={options.statusBarColor}\n            statusBarHidden={options.statusBarHidden}\n            statusBarStyle={options.statusBarStyle}\n            statusBarTranslucent={options.statusBarTranslucent}\n            onAppear={() => onAppear(route, descriptor, routeNavigationProp)}\n            onWillAppear={() => options?.onWillAppear?.()}\n            onWillDisappear={() => options?.onWillDisappear?.()}\n            onDisappear={() => options?.onDisappear?.()}\n            onHeaderBackButtonClicked={() =>\n              removeScene(route, 1, routeNavigationProp)\n            }\n            onDismissed={(e) =>\n              removeScene(\n                route,\n                e.nativeEvent.dismissCount,\n                routeNavigationProp\n              )\n            }>\n            {isHeaderInPush &&\n              renderHeaderConfig(index, route, descriptor, navigationConfig)}\n            <MaybeNestedStack\n              isHeaderInModal={isHeaderInModal}\n              screenProps={screenProps}\n              route={route}\n              navigation={routeNavigationProp}\n              SceneComponent={SceneComponent}\n              index={index}\n              descriptor={descriptor}\n              navigationConfig={navigationConfig}\n            />\n          </Screen>\n        );\n      })}\n    </ScreenStack>\n  );\n}\n\nconst styles = StyleSheet.create({\n  scenes: { flex: 1 },\n});\n\nfunction createStackNavigator(\n  routeConfigMap: NavigationRouteConfigMap<\n    NativeStackNavigationOptions,\n    StackNavigationProp\n  >,\n  stackConfig: CreateNavigatorConfig<\n    NativeStackNavigationConfig,\n    NavigationStackRouterConfig,\n    NativeStackNavigationOptions,\n    StackNavigationProp\n  > = {}\n): NavigationNavigator<\n  Record<string, unknown>,\n  NavigationProp<NavigationState>\n> {\n  const router = StackRouter(routeConfigMap, stackConfig);\n\n  // below we override getStateForAction method in order to add handling for\n  // a custom native stack navigation action. The action REMOVE that we want to\n  // add works in a similar way to POP, but it does not remove all the routes\n  // that sit on top of the removed route. For example if we have three routes\n  // [a,b,c] and call POP on b, then both b and c will go away. In case we\n  // call REMOVE on b, only b will be removed from the stack and the resulting\n  // state will be [a, c]\n  const superGetStateForAction = router.getStateForAction;\n  router.getStateForAction = (\n    action: NavigationAction | NativeStackRemoveNavigationAction,\n    state\n  ) => {\n    if (action.type === REMOVE_ACTION) {\n      const { key, immediate, dismissCount } = action;\n      let backRouteIndex = state.index;\n      if (key) {\n        const backRoute = state.routes.find(\n          (route: NavigationRoute<NavigationParams>) => route.key === key\n        );\n        backRouteIndex = state.routes.indexOf(backRoute);\n      }\n\n      if (backRouteIndex > 0) {\n        const newRoutes = [...state.routes];\n        if (dismissCount > 1) {\n          // when dismissing with iOS 14 native header back button, we can pop more than 1 screen at a time\n          // and the `backRouteIndex` is the index of the previous screen. Since we are starting already\n          // on the previous screen, we add 1 to start.\n          newRoutes.splice(backRouteIndex - dismissCount + 1, dismissCount);\n        } else {\n          newRoutes.splice(backRouteIndex, 1);\n        }\n\n        return {\n          ...state,\n          routes: newRoutes,\n          index: newRoutes.length - 1,\n          isTransitioning: immediate !== true,\n        };\n      }\n    }\n    return superGetStateForAction(action as NavigationAction, state);\n  };\n  // Create a navigator with StackView as the view\n  return createNavigator(StackView, router, stackConfig);\n}\n\nexport default createStackNavigator;\n"]}
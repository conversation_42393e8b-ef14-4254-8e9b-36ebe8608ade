{"version": 3, "sources": ["utils.ts"], "names": ["isSearchBarAvailableForCurrentPlatform", "includes", "Platform", "OS", "executeNativeBackPress", "<PERSON><PERSON><PERSON><PERSON>", "exitApp"], "mappings": ";;;;;;;;AAAA;;AAEO,MAAMA,sCAAsC,GAAG,CACpD,KADoD,EAEpD,SAFoD,EAGpDC,QAHoD,CAG3CC,sBAASC,EAHkC,CAA/C;;;AAKA,SAASC,sBAAT,GAAkC;AACvC;AACAC,2BAAYC,OAAZ;;AACA,SAAO,IAAP;AACD", "sourcesContent": ["import { BackHandler, Platform } from 'react-native';\n\nexport const isSearchBarAvailableForCurrentPlatform = [\n  'ios',\n  'android',\n].includes(Platform.OS);\n\nexport function executeNativeBackPress() {\n  // This function invokes the native back press event\n  BackHandler.exitApp();\n  return true;\n}\n"]}
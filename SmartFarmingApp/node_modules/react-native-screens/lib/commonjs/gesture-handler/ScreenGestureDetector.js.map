{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNative", "_reactNativeGestureHandler", "_reactNativeReanimated", "_fabricUtils", "_RNScreensTurboModule", "_defaults", "_constraints", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "EmptyGestureHandler", "Gesture", "Fling", "ScreenGestureDetector", "_ref", "children", "gestureDetectorBridge", "goBackGesture", "screenEdgeGesture", "transitionAnimation", "customTransitionAnimation", "screensRefs", "currentRouteKey", "sharedEvent", "useSharedValue", "DefaultEvent", "startingGesturePosition", "canPerformUpdates", "makeMutable", "getAnimationForTransition", "screenTransitionConfig", "stackTag", "belowTopScreenId", "topScreenId", "screenTransition", "isTransitionCanceled", "screenDimensions", "DefaultScreenDimensions", "onFinishAnimation", "screenTagToNodeWrapperUI", "IS_FABRIC", "isF<PERSON><PERSON>", "current", "stackUseEffectCallback", "stackRef", "value", "findNodeHandle", "Platform", "OS", "runOnUI", "RNScreensTurboModule", "disableSwipeBackForTopScreen", "useEffect", "screenTagToNodeWrapper", "key", "screenRef", "screenData", "getShadowNodeWrapperAndTagFromRef", "tag", "shadowNodeWrapper", "console", "warn", "computeProgress", "event", "progress", "startingPosition", "translationX", "width", "absoluteX", "translationY", "height", "absoluteY", "Math", "abs", "progressX", "progressY", "max", "onStart", "transitionConfig", "transitionData", "startTransition", "canStartTransition", "topScreenTag", "belowTopScreenTag", "animatedRefMock", "screenSize", "measure", "Error", "finishTransition", "startScreenTransition", "onUpdate", "checkBoundaries", "updateTransition", "onEnd", "velocityFactor", "distanceX", "min", "velocityX", "distanceY", "velocityY", "requiredXDistance", "requiredYDistance", "checkIfTransitionCancelled", "finishScreenTransition", "panGesture", "Pan", "HIT_SLOP_SIZE", "ACTIVATION_DISTANCE", "activeOffsetX", "hitSlop", "left", "top", "right", "activeOffsetY", "Dimensions", "bottom", "createElement", "GestureDetector", "gesture", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["gesture-handler/ScreenGestureDetector.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,0BAAA,GAAAF,OAAA;AAMA,IAAAG,sBAAA,GAAAH,OAAA;AASA,IAAAI,YAAA,GAAAJ,OAAA;AACA,IAAAK,qBAAA,GAAAL,OAAA;AACA,IAAAM,SAAA,GAAAN,OAAA;AACA,IAAAO,YAAA,GAAAP,OAAA;AAIuB,SAAAQ,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAV,wBAAAU,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAEvB,MAAMY,mBAAmB,GAAGC,kCAAO,CAACC,KAAK,CAAC,CAAC;AAE3C,MAAMC,qBAAqB,GAAGC,IAAA,IAQF;EAAA,IARG;IAC7BC,QAAQ;IACRC,qBAAqB;IACrBC,aAAa;IACbC,iBAAiB;IACjBC,mBAAmB,EAAEC,yBAAyB;IAC9CC,WAAW;IACXC;EACoB,CAAC,GAAAR,IAAA;EACrB,MAAMS,WAAW,GAAG,IAAAC,qCAAc,EAACC,sBAAY,CAAC;EAChD,MAAMC,uBAAuB,GAAG,IAAAF,qCAAc,EAACC,sBAAY,CAAC;EAC5D,MAAME,iBAAiB,GAAG,IAAAC,kCAAW,EAAC,KAAK,CAAC;EAC5C,MAAMT,mBAAmB,GAAG,IAAAU,sCAAyB,EACnDZ,aAAa,EACbG,yBACF,CAAC;EACD,MAAMU,sBAAsB,GAAG,IAAAF,kCAAW,EAAC;IACzCG,QAAQ,EAAE,CAAC,CAAC;IACZC,gBAAgB,EAAE,CAAC,CAAC;IACpBC,WAAW,EAAE,CAAC,CAAC;IACfV,WAAW;IACXG,uBAAuB;IACvBQ,gBAAgB,EAAEf,mBAAmB;IACrCgB,oBAAoB,EAAE,KAAK;IAC3BlB,aAAa,EAAEA,aAAa,IAAI,YAAY;IAC5CmB,gBAAgB,EAAEC,iCAAuB;IACzCC,iBAAiB,EAAEA,CAAA,KAAM;MACvB,SAAS;IACX;EACF,CAAC,CAAC;EACF,MAAMP,QAAQ,GAAG,IAAAH,kCAAW,EAAC,CAAC,CAAC,CAAC;EAChC,MAAMW,wBAAwB,GAAG,IAAAX,kCAAW,EAAsB,CAAC,CAAC,CAAC;EACrE,MAAMY,SAAS,GAAG,IAAAC,qBAAQ,EAAC,CAAC;EAE5BzB,qBAAqB,CAAC0B,OAAO,CAACC,sBAAsB,GAAGC,QAAQ,IAAI;IACjE,IAAI,CAAC3B,aAAa,EAAE;MAClB;IACF;IACAc,QAAQ,CAACc,KAAK,GAAG,IAAAC,2BAAc,EAACF,QAAQ,CAACF,OAAc,CAAW;IAClE,IAAIK,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MACzB,IAAAC,8BAAO,EAAC,MAAM;QACZC,0CAAoB,CAACC,4BAA4B,CAACpB,QAAQ,CAACc,KAAK,CAAC;MACnE,CAAC,CAAC,CAAC,CAAC;IACN;EACF,CAAC;EAED,IAAAO,gBAAS,EAAC,MAAM;IACd,IAAI,CAACZ,SAAS,IAAI,CAACvB,aAAa,EAAE;MAChC;IACF;IACA,MAAMoC,sBAA+D,GAAG,CAAC,CAAC;IAC1E,KAAK,MAAMC,GAAG,IAAIjC,WAAW,CAACqB,OAAO,EAAE;MACrC,MAAMa,SAAS,GAAGlC,WAAW,CAACqB,OAAO,CAACY,GAAG,CAAC;MAC1C,MAAME,UAAU,GAAG,IAAAC,8CAAiC,EAACF,SAAS,CAACb,OAAO,CAAC;MACvE,IAAIc,UAAU,CAACE,GAAG,IAAIF,UAAU,CAACG,iBAAiB,EAAE;QAClDN,sBAAsB,CAACG,UAAU,CAACE,GAAG,CAAC,GAAGF,UAAU,CAACG,iBAAiB;MACvE,CAAC,MAAM;QACLC,OAAO,CAACC,IAAI,CAAC,4CAA4C,CAAC;MAC5D;IACF;IACAtB,wBAAwB,CAACM,KAAK,GAAGQ,sBAAsB;EACzD,CAAC,EAAE,CAAC/B,eAAe,CAAC,CAAC;EAErB,SAASwC,eAAeA,CACtBC,KAAwD,EACxD;IACA,SAAS;;IACT,IAAIC,QAAQ,GAAG,CAAC;IAChB,MAAM5B,gBAAgB,GAAGN,sBAAsB,CAACe,KAAK,CAACT,gBAAgB;IACtE,MAAM6B,gBAAgB,GAAGvC,uBAAuB,CAACmB,KAAK;IACtD,IAAI5B,aAAa,KAAK,YAAY,EAAE;MAClC+C,QAAQ,GACND,KAAK,CAACG,YAAY,IACjB9B,gBAAgB,CAAC+B,KAAK,GAAGF,gBAAgB,CAACG,SAAS,CAAC;IACzD,CAAC,MAAM,IAAInD,aAAa,KAAK,WAAW,EAAE;MACxC+C,QAAQ,GAAI,CAAC,CAAC,GAAGD,KAAK,CAACG,YAAY,GAAID,gBAAgB,CAACG,SAAS;IACnE,CAAC,MAAM,IAAInD,aAAa,KAAK,WAAW,EAAE;MACxC+C,QAAQ,GACL,CAAC,CAAC,GAAGD,KAAK,CAACM,YAAY,IACvBjC,gBAAgB,CAACkC,MAAM,GAAGL,gBAAgB,CAACM,SAAS,CAAC;IAC1D,CAAC,MAAM,IAAItD,aAAa,KAAK,SAAS,EAAE;MACtC+C,QAAQ,GAAGD,KAAK,CAACM,YAAY,GAAGJ,gBAAgB,CAACM,SAAS;IAC5D,CAAC,MAAM,IAAItD,aAAa,KAAK,iBAAiB,EAAE;MAC9C+C,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAACV,KAAK,CAACG,YAAY,GAAG9B,gBAAgB,CAAC+B,KAAK,GAAG,CAAC,CAAC;IACtE,CAAC,MAAM,IAAIlD,aAAa,KAAK,eAAe,EAAE;MAC5C+C,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAACV,KAAK,CAACM,YAAY,GAAGjC,gBAAgB,CAACkC,MAAM,GAAG,CAAC,CAAC;IACvE,CAAC,MAAM,IAAIrD,aAAa,KAAK,qBAAqB,EAAE;MAClD,MAAMyD,SAAS,GAAGF,IAAI,CAACC,GAAG,CACxBV,KAAK,CAACG,YAAY,GAAG9B,gBAAgB,CAAC+B,KAAK,GAAG,CAChD,CAAC;MACD,MAAMQ,SAAS,GAAGH,IAAI,CAACC,GAAG,CACxBV,KAAK,CAACM,YAAY,GAAGjC,gBAAgB,CAACkC,MAAM,GAAG,CACjD,CAAC;MACDN,QAAQ,GAAGQ,IAAI,CAACI,GAAG,CAACF,SAAS,EAAEC,SAAS,CAAC;IAC3C;IACA,OAAOX,QAAQ;EACjB;EAEA,SAASa,OAAOA,CAACd,KAAwD,EAAE;IACzE,SAAS;;IACTxC,WAAW,CAACsB,KAAK,GAAGkB,KAAK;IACzB,MAAMe,gBAAgB,GAAGhD,sBAAsB,CAACe,KAAK;IACrD,MAAMkC,cAAc,GAAG7B,0CAAoB,CAAC8B,eAAe,CAACjD,QAAQ,CAACc,KAAK,CAAC;IAC3E,IAAIkC,cAAc,CAACE,kBAAkB,KAAK,KAAK,EAAE;MAC/CtD,iBAAiB,CAACkB,KAAK,GAAG,KAAK;MAC/B;IACF;IAEA,IAAIL,SAAS,EAAE;MACbsC,gBAAgB,CAAC7C,WAAW,GAC1BM,wBAAwB,CAACM,KAAK,CAACkC,cAAc,CAACG,YAAY,CAAC;MAC7DJ,gBAAgB,CAAC9C,gBAAgB,GAC/BO,wBAAwB,CAACM,KAAK,CAACkC,cAAc,CAACI,iBAAiB,CAAC;IACpE,CAAC,MAAM;MACLL,gBAAgB,CAAC7C,WAAW,GAAG8C,cAAc,CAACG,YAAY;MAC1DJ,gBAAgB,CAAC9C,gBAAgB,GAAG+C,cAAc,CAACI,iBAAiB;IACtE;IAEAL,gBAAgB,CAAC/C,QAAQ,GAAGA,QAAQ,CAACc,KAAK;IAC1CnB,uBAAuB,CAACmB,KAAK,GAAGkB,KAAK;IACrC,MAAMqB,eAAe,GAAGA,CAAA,KAAM;MAC5B,OAAOtD,sBAAsB,CAACe,KAAK,CAACZ,WAAW;IACjD,CAAC;IACD,MAAMoD,UAAU,GAAG,IAAAC,8BAAO,EAACF,eAAsB,CAAC;IAClD,IAAIC,UAAU,IAAI,IAAI,EAAE;MACtB,MAAM,IAAIE,KAAK,CAAC,uCAAuC,CAAC;IAC1D;IACA,IAAIF,UAAU,IAAI,IAAI,EAAE;MACtB1D,iBAAiB,CAACkB,KAAK,GAAG,KAAK;MAC/BK,0CAAoB,CAACsC,gBAAgB,CAACzD,QAAQ,CAACc,KAAK,EAAE,IAAI,CAAC;MAC3D;IACF;IACAiC,gBAAgB,CAAC1C,gBAAgB,GAAGiD,UAAU;IAC9C;IACA;IACA;IACA;IACA;IACA;IACA,IAAAI,4CAAqB,EAACX,gBAAuB,CAAC;IAC9CnD,iBAAiB,CAACkB,KAAK,GAAG,IAAI;EAChC;EAEA,SAAS6C,QAAQA,CAAC3B,KAAwD,EAAE;IAC1E,SAAS;;IACT,IAAI,CAACpC,iBAAiB,CAACkB,KAAK,EAAE;MAC5B;IACF;IACA,IAAA8C,4BAAe,EAAC1E,aAAa,EAAE8C,KAAK,CAAC;IACrC,MAAMC,QAAQ,GAAGF,eAAe,CAACC,KAAK,CAAC;IACvCxC,WAAW,CAACsB,KAAK,GAAGkB,KAAK;IACzB,MAAMhC,QAAQ,GAAGD,sBAAsB,CAACe,KAAK,CAACd,QAAQ;IACtDmB,0CAAoB,CAAC0C,gBAAgB,CAAC7D,QAAQ,EAAEiC,QAAQ,CAAC;EAC3D;EAEA,SAAS6B,KAAKA,CAAC9B,KAAwD,EAAE;IACvE,SAAS;;IACT,IAAI,CAACpC,iBAAiB,CAACkB,KAAK,EAAE;MAC5B;IACF;IAEA,MAAMiD,cAAc,GAAG,GAAG;IAC1B,MAAMT,UAAU,GAAGvD,sBAAsB,CAACe,KAAK,CAACT,gBAAgB;IAChE,MAAM2D,SAAS,GACbhC,KAAK,CAACG,YAAY,GAAGM,IAAI,CAACwB,GAAG,CAACjC,KAAK,CAACkC,SAAS,GAAGH,cAAc,EAAE,GAAG,CAAC;IACtE,MAAMI,SAAS,GACbnC,KAAK,CAACM,YAAY,GAAGG,IAAI,CAACwB,GAAG,CAACjC,KAAK,CAACoC,SAAS,GAAGL,cAAc,EAAE,GAAG,CAAC;IACtE,MAAMM,iBAAiB,GAAGf,UAAU,CAAClB,KAAK,GAAG,CAAC;IAC9C,MAAMkC,iBAAiB,GAAGhB,UAAU,CAACf,MAAM,GAAG,CAAC;IAC/C,MAAMnC,oBAAoB,GAAG,IAAAmE,uCAA0B,EACrDrF,aAAa,EACb8E,SAAS,EACTK,iBAAiB,EACjBF,SAAS,EACTG,iBACF,CAAC;IACD,MAAMtE,QAAQ,GAAGD,sBAAsB,CAACe,KAAK,CAACd,QAAQ;IACtDD,sBAAsB,CAACe,KAAK,CAACP,iBAAiB,GAAG,MAAM;MACrDY,0CAAoB,CAACsC,gBAAgB,CAACzD,QAAQ,EAAEI,oBAAoB,CAAC;IACvE,CAAC;IACDL,sBAAsB,CAACe,KAAK,CAACV,oBAAoB,GAAGA,oBAAoB;IACxE;IACA;IACA;IACA;IACA;IACA;IACA,IAAAoE,6CAAsB,EAACzE,sBAAsB,CAACe,KAAY,CAAC;EAC7D;EAEA,IAAI2D,UAAU,GAAG7F,kCAAO,CAAC8F,GAAG,CAAC,CAAC,CAC3B5B,OAAO,CAACA,OAAO,CAAC,CAChBa,QAAQ,CAACA,QAAQ,CAAC,CAClBG,KAAK,CAACA,KAAK,CAAC;EAEf,IAAI3E,iBAAiB,EAAE;IACrB,MAAMwF,aAAa,GAAG,EAAE;IACxB,MAAMC,mBAAmB,GAAG,EAAE;IAC9B,IAAI1F,aAAa,KAAK,YAAY,EAAE;MAClCuF,UAAU,GAAGA,UAAU,CACpBI,aAAa,CAACD,mBAAmB,CAAC,CAClCE,OAAO,CAAC;QAAEC,IAAI,EAAE,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAE5C,KAAK,EAAEuC;MAAc,CAAC,CAAC;IACvD,CAAC,MAAM,IAAIzF,aAAa,KAAK,WAAW,EAAE;MACxCuF,UAAU,GAAGA,UAAU,CACpBI,aAAa,CAAC,CAACD,mBAAmB,CAAC,CACnCE,OAAO,CAAC;QAAEG,KAAK,EAAE,CAAC;QAAED,GAAG,EAAE,CAAC;QAAE5C,KAAK,EAAEuC;MAAc,CAAC,CAAC;IACxD,CAAC,MAAM,IAAIzF,aAAa,KAAK,WAAW,EAAE;MACxCuF,UAAU,GAAGA,UAAU,CACpBS,aAAa,CAACN,mBAAmB,CAAC,CAClCE,OAAO,CAAC;QAAEE,GAAG,EAAE,CAAC;QAAEzC,MAAM,EAAE4C,uBAAU,CAACrH,GAAG,CAAC,QAAQ,CAAC,CAACyE,MAAM,GAAG;MAAI,CAAC,CAAC;MACrE;IACF,CAAC,MAAM,IAAIrD,aAAa,KAAK,SAAS,EAAE;MACtCuF,UAAU,GAAGA,UAAU,CACpBS,aAAa,CAAC,CAACN,mBAAmB,CAAC,CACnCE,OAAO,CAAC;QAAEM,MAAM,EAAE,CAAC;QAAE7C,MAAM,EAAEoC;MAAc,CAAC,CAAC;IAClD;EACF;EACA,oBACE/H,MAAA,CAAAgB,OAAA,CAAAyH,aAAA,CAACrI,0BAAA,CAAAsI,eAAe;IAACC,OAAO,EAAErG,aAAa,GAAGuF,UAAU,GAAG9F;EAAoB,GACxEK,QACc,CAAC;AAEtB,CAAC;AAAC,IAAAwG,QAAA,GAAAC,OAAA,CAAA7H,OAAA,GAEakB,qBAAqB"}
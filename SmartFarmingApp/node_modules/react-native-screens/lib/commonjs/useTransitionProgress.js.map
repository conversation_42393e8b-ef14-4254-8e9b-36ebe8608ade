{"version": 3, "sources": ["useTransitionProgress.tsx"], "names": ["useTransitionProgress", "progress", "React", "useContext", "TransitionProgressContext", "undefined", "Error"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;;;;AAEe,SAASA,qBAAT,GAAiC;AAC9C,QAAMC,QAAQ,GAAGC,KAAK,CAACC,UAAN,CAAiBC,kCAAjB,CAAjB;;AAEA,MAAIH,QAAQ,KAAKI,SAAjB,EAA4B;AAC1B,UAAM,IAAIC,KAAJ,CACJ,wFADI,CAAN;AAGD;;AAED,SAAOL,QAAP;AACD", "sourcesContent": ["import * as React from 'react';\n\nimport TransitionProgressContext from './TransitionProgressContext';\n\nexport default function useTransitionProgress() {\n  const progress = React.useContext(TransitionProgressContext);\n\n  if (progress === undefined) {\n    throw new Error(\n      \"Couldn't find values for transition progress. Are you inside a screen in Native Stack?\"\n    );\n  }\n\n  return progress;\n}\n"]}
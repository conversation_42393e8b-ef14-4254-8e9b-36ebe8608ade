{"version": 3, "sources": ["NativeStackView.tsx"], "names": ["isAndroid", "Platform", "OS", "Container", "View", "__DEV__", "DebugContainer", "props", "stackPresentation", "rest", "MaybeNestedStack", "options", "route", "children", "colors", "headerShown", "contentStyle", "Screen", "React", "useContext", "ScreenContext", "isHeaderInModal", "headerShownPreviousRef", "useRef", "useEffect", "current", "name", "content", "styles", "container", "backgroundColor", "background", "topInset", "top", "dimensions", "headerHeight", "StyleSheet", "absoluteFill", "RouteView", "descriptors", "index", "navigation", "stateKey", "render", "renderScene", "key", "gestureEnabled", "hideKeyboardOnSwipe", "homeIndicatorHidden", "nativeBackButtonDismissalEnabled", "navigationBarColor", "navigationBarHidden", "replaceAnimation", "screenOrientation", "statusBarAnimation", "statusBarColor", "statusBarHidden", "statusBarStyle", "statusBarTranslucent", "swipeDirection", "transitionDuration", "freezeOnBlur", "customAnimationOnSwipe", "fullScreenSwipeEnabled", "gestureResponseDistance", "stackAnimation", "undefined", "isHeaderInPush", "parentHeaderHeight", "HeaderHeightContext", "dispatch", "StackActions", "pop", "source", "target", "emit", "type", "data", "closing", "e", "dismissCount", "nativeEvent", "NativeStackViewInner", "state", "routes", "map", "NativeStackView", "create", "flex"], "mappings": ";;;;;;;AAAA;;AACA;;AAEA;;AACA;;AACA;;AAKA;;AASA;;AAUA;;AACA;;AACA;;AACA;;;;;;;;;;AAEA,MAAMA,SAAS,GAAGC,sBAASC,EAAT,KAAgB,SAAlC;AAEA,IAAIC,SAAS,GAAGC,iBAAhB;;AAEA,IAAIC,OAAJ,EAAa;AACX,QAAMC,cAAc,GAClBC,KADqB,IAElB;AACH,UAAM;AAAEC,MAAAA,iBAAF;AAAqB,SAAGC;AAAxB,QAAiCF,KAAvC;;AACA,QAAIN,sBAASC,EAAT,KAAgB,KAAhB,IAAyBM,iBAAiB,KAAK,MAAnD,EAA2D;AACzD,0BACE,oBAAC,qBAAD,qBACE,oBAAC,iBAAD,EAAUC,IAAV,CADF,CADF;AAKD;;AACD,wBAAO,oBAAC,iBAAD,EAAUA,IAAV,CAAP;AACD,GAZD,CADW,CAcX;;;AACAN,EAAAA,SAAS,GAAGG,cAAZ;AACD;;AAED,MAAMI,gBAAgB,GAAG,CAAC;AACxBC,EAAAA,OADwB;AAExBC,EAAAA,KAFwB;AAGxBJ,EAAAA,iBAHwB;AAIxBK,EAAAA;AAJwB,CAAD,KAUnB;AACJ,QAAM;AAAEC,IAAAA;AAAF,MAAa,uBAAnB;AACA,QAAM;AAAEC,IAAAA,WAAW,GAAG,IAAhB;AAAsBC,IAAAA;AAAtB,MAAuCL,OAA7C;AAEA,QAAMM,MAAM,GAAGC,KAAK,CAACC,UAAN,CAAiBC,iCAAjB,CAAf;AAEA,QAAMC,eAAe,GAAGrB,SAAS,GAC7B,KAD6B,GAE7BQ,iBAAiB,KAAK,MAAtB,IAAgCO,WAAW,KAAK,IAFpD;AAIA,QAAMO,sBAAsB,GAAGJ,KAAK,CAACK,MAAN,CAAaR,WAAb,CAA/B;AAEAG,EAAAA,KAAK,CAACM,SAAN,CAAgB,MAAM;AACpB,2BACE,CAACxB,SAAD,IACEQ,iBAAiB,KAAK,MADxB,IAEEc,sBAAsB,CAACG,OAAvB,KAAmCV,WAHvC,EAIG,6IAA4IH,KAAK,CAACc,IAAK,IAJ1J;AAOAJ,IAAAA,sBAAsB,CAACG,OAAvB,GAAiCV,WAAjC;AACD,GATD,EASG,CAACA,WAAD,EAAcP,iBAAd,EAAiCI,KAAK,CAACc,IAAvC,CATH;AAWA,QAAMC,OAAO,gBACX,oBAAC,SAAD;AACE,IAAA,KAAK,EAAE,CACLC,MAAM,CAACC,SADF,EAELrB,iBAAiB,KAAK,kBAAtB,IACEA,iBAAiB,KAAK,2BADxB,IACuD;AACnDsB,MAAAA,eAAe,EAAEhB,MAAM,CAACiB;AAD2B,KAHlD,EAMLf,YANK,CADT,CASE;AATF;AAUE,IAAA,iBAAiB,EAAER;AAVrB,KAWGK,QAXH,CADF;AAgBA,QAAMmB,QAAQ,GAAG,qDAAoBC,GAArC;AACA,QAAMC,UAAU,GAAG,mDAAnB;AACA,QAAMC,YAAY,GAAG,qCACnBD,UADmB,EAEnBF,QAFmB,EAGnBxB,iBAHmB,CAArB;;AAMA,MAAIa,eAAJ,EAAqB;AACnB,wBACE,oBAAC,+BAAD;AAAa,MAAA,KAAK,EAAEO,MAAM,CAACC;AAA3B,oBACE,oBAAC,MAAD;AAAQ,MAAA,OAAO,MAAf;AAAgB,MAAA,aAAa,MAA7B;AAA8B,MAAA,KAAK,EAAEO,wBAAWC;AAAhD,oBACE,oBAAC,4BAAD,CAAqB,QAArB;AAA8B,MAAA,KAAK,EAAEF;AAArC,oBACE,oBAAC,qBAAD,eAAkBxB,OAAlB;AAA2B,MAAA,KAAK,EAAEC;AAAlC,OADF,EAEGe,OAFH,CADF,CADF,CADF;AAUD;;AAED,SAAOA,OAAP;AACD,CAvED;;AAgFA,MAAMW,SAAS,GAAG,CAAC;AACjBC,EAAAA,WADiB;AAEjB3B,EAAAA,KAFiB;AAGjB4B,EAAAA,KAHiB;AAIjBC,EAAAA,UAJiB;AAKjBC,EAAAA;AALiB,CAAD,KAYZ;AACJ,QAAM;AAAE/B,IAAAA,OAAF;AAAWgC,IAAAA,MAAM,EAAEC;AAAnB,MAAmCL,WAAW,CAAC3B,KAAK,CAACiC,GAAP,CAApD;AACA,QAAM;AACJC,IAAAA,cADI;AAEJ/B,IAAAA,WAFI;AAGJgC,IAAAA,mBAHI;AAIJC,IAAAA,mBAJI;AAKJC,IAAAA,gCAAgC,GAAG,KAL/B;AAMJC,IAAAA,kBANI;AAOJC,IAAAA,mBAPI;AAQJC,IAAAA,gBAAgB,GAAG,KARf;AASJC,IAAAA,iBATI;AAUJC,IAAAA,kBAVI;AAWJC,IAAAA,cAXI;AAYJC,IAAAA,eAZI;AAaJC,IAAAA,cAbI;AAcJC,IAAAA,oBAdI;AAeJC,IAAAA,cAAc,GAAG,YAfb;AAgBJC,IAAAA,kBAhBI;AAiBJC,IAAAA;AAjBI,MAkBFlD,OAlBJ;AAoBA,MAAI;AACFmD,IAAAA,sBADE;AAEFC,IAAAA,sBAFE;AAGFC,IAAAA,uBAHE;AAIFC,IAAAA,cAJE;AAKFzD,IAAAA,iBAAiB,GAAG;AALlB,MAMAG,OANJ;;AAQA,MAAIgD,cAAc,KAAK,UAAvB,EAAmC;AACjC;AACA;AACA;AACA;AACA;AACA,QAAII,sBAAsB,KAAKG,SAA/B,EAA0C;AACxCH,MAAAA,sBAAsB,GAAG,IAAzB;AACD;;AACD,QAAID,sBAAsB,KAAKI,SAA/B,EAA0C;AACxCJ,MAAAA,sBAAsB,GAAG,IAAzB;AACD;;AACD,QAAIG,cAAc,KAAKC,SAAvB,EAAkC;AAChCD,MAAAA,cAAc,GAAG,mBAAjB;AACD;AACF;;AAED,MAAIzB,KAAK,KAAK,CAAd,EAAiB;AACf;AACA;AACAhC,IAAAA,iBAAiB,GAAG,MAApB;AACD;;AAED,QAAM2D,cAAc,GAAGnE,SAAS,GAC5Be,WAD4B,GAE5BP,iBAAiB,KAAK,MAAtB,IAAgCO,WAAW,KAAK,KAFpD;AAIA,QAAMmB,UAAU,GAAG,mDAAnB;AACA,QAAMF,QAAQ,GAAG,qDAAoBC,GAArC;AACA,QAAME,YAAY,GAAG,qCACnBD,UADmB,EAEnBF,QAFmB,EAGnBxB,iBAHmB,CAArB;AAKA,QAAM4D,kBAAkB,GAAGlD,KAAK,CAACC,UAAN,CAAiBkD,4BAAjB,CAA3B;AACA,QAAMpD,MAAM,GAAGC,KAAK,CAACC,UAAN,CAAiBC,iCAAjB,CAAf;AAEA,sBACE,oBAAC,MAAD;AACE,IAAA,GAAG,EAAER,KAAK,CAACiC,GADb;AAEE,IAAA,OAAO,MAFT;AAGE,IAAA,aAAa,MAHf;AAIE,IAAA,KAAK,EAAET,wBAAWC,YAJpB;AAKE,IAAA,sBAAsB,EAAEyB,sBAL1B;AAME,IAAA,YAAY,EAAED,YANhB;AAOE,IAAA,sBAAsB,EAAEE,sBAP1B;AAQE,IAAA,mBAAmB,EAAEhB,mBARvB;AASE,IAAA,mBAAmB,EAAEC,mBATvB;AAUE,IAAA,cAAc,EAAEhD,SAAS,GAAG,KAAH,GAAW8C,cAVtC;AAWE,IAAA,uBAAuB,EAAEkB,uBAX3B;AAYE,IAAA,gCAAgC,EAAEf,gCAZpC;AAaE,IAAA,kBAAkB,EAAEC,kBAbtB;AAcE,IAAA,mBAAmB,EAAEC,mBAdvB;AAeE,IAAA,gBAAgB,EAAEC,gBAfpB;AAgBE,IAAA,iBAAiB,EAAEC,iBAhBrB;AAiBE,IAAA,cAAc,EAAEY,cAjBlB;AAkBE,IAAA,iBAAiB,EAAEzD,iBAlBrB;AAmBE,IAAA,kBAAkB,EAAE8C,kBAnBtB;AAoBE,IAAA,cAAc,EAAEC,cApBlB;AAqBE,IAAA,eAAe,EAAEC,eArBnB;AAsBE,IAAA,cAAc,EAAEC,cAtBlB;AAuBE,IAAA,oBAAoB,EAAEC,oBAvBxB;AAwBE,IAAA,cAAc,EAAEC,cAxBlB;AAyBE,IAAA,kBAAkB,EAAEC,kBAzBtB;AA0BE,IAAA,yBAAyB,EAAE,MAAM;AAC/BnB,MAAAA,UAAU,CAAC6B,QAAX,CAAoB,EAClB,GAAGC,qBAAaC,GAAb,EADe;AAElBC,QAAAA,MAAM,EAAE7D,KAAK,CAACiC,GAFI;AAGlB6B,QAAAA,MAAM,EAAEhC;AAHU,OAApB;AAKD,KAhCH;AAiCE,IAAA,YAAY,EAAE,MAAM;AAClBD,MAAAA,UAAU,CAACkC,IAAX,CAAgB;AACdC,QAAAA,IAAI,EAAE,iBADQ;AAEdC,QAAAA,IAAI,EAAE;AAAEC,UAAAA,OAAO,EAAE;AAAX,SAFQ;AAGdJ,QAAAA,MAAM,EAAE9D,KAAK,CAACiC;AAHA,OAAhB;AAKD,KAvCH;AAwCE,IAAA,eAAe,EAAE,MAAM;AACrBJ,MAAAA,UAAU,CAACkC,IAAX,CAAgB;AACdC,QAAAA,IAAI,EAAE,iBADQ;AAEdC,QAAAA,IAAI,EAAE;AAAEC,UAAAA,OAAO,EAAE;AAAX,SAFQ;AAGdJ,QAAAA,MAAM,EAAE9D,KAAK,CAACiC;AAHA,OAAhB;AAKD,KA9CH;AA+CE,IAAA,QAAQ,EAAE,MAAM;AACdJ,MAAAA,UAAU,CAACkC,IAAX,CAAgB;AACdC,QAAAA,IAAI,EAAE,QADQ;AAEdF,QAAAA,MAAM,EAAE9D,KAAK,CAACiC;AAFA,OAAhB;AAIAJ,MAAAA,UAAU,CAACkC,IAAX,CAAgB;AACdC,QAAAA,IAAI,EAAE,eADQ;AAEdC,QAAAA,IAAI,EAAE;AAAEC,UAAAA,OAAO,EAAE;AAAX,SAFQ;AAGdJ,QAAAA,MAAM,EAAE9D,KAAK,CAACiC;AAHA,OAAhB;AAKD,KAzDH;AA0DE,IAAA,WAAW,EAAE,MAAM;AACjBJ,MAAAA,UAAU,CAACkC,IAAX,CAAgB;AACdC,QAAAA,IAAI,EAAE,eADQ;AAEdC,QAAAA,IAAI,EAAE;AAAEC,UAAAA,OAAO,EAAE;AAAX,SAFQ;AAGdJ,QAAAA,MAAM,EAAE9D,KAAK,CAACiC;AAHA,OAAhB;AAKD,KAhEH;AAiEE,IAAA,WAAW,EAAGkC,CAAD,IAAO;AAClBtC,MAAAA,UAAU,CAACkC,IAAX,CAAgB;AACdC,QAAAA,IAAI,EAAE,SADQ;AAEdF,QAAAA,MAAM,EAAE9D,KAAK,CAACiC;AAFA,OAAhB;AAKA,YAAMmC,YAAY,GAChBD,CAAC,CAACE,WAAF,CAAcD,YAAd,GAA6B,CAA7B,GAAiCD,CAAC,CAACE,WAAF,CAAcD,YAA/C,GAA8D,CADhE;AAGAvC,MAAAA,UAAU,CAAC6B,QAAX,CAAoB,EAClB,GAAGC,qBAAaC,GAAb,CAAiBQ,YAAjB,CADe;AAElBP,QAAAA,MAAM,EAAE7D,KAAK,CAACiC,GAFI;AAGlB6B,QAAAA,MAAM,EAAEhC;AAHU,OAApB;AAKD;AA/EH,kBAgFE,oBAAC,4BAAD,CAAqB,QAArB;AACE,IAAA,KAAK,EACHyB,cAAc,KAAK,KAAnB,GAA2BhC,YAA3B,GAA0CiC,kBAA1C,aAA0CA,kBAA1C,cAA0CA,kBAA1C,GAAgE;AAFpE,kBAIE,oBAAC,qBAAD,eAAkBzD,OAAlB;AAA2B,IAAA,KAAK,EAAEC,KAAlC;AAAyC,IAAA,WAAW,EAAEuD;AAAtD,KAJF,eAKE,oBAAC,gBAAD;AACE,IAAA,OAAO,EAAExD,OADX;AAEE,IAAA,KAAK,EAAEC,KAFT;AAGE,IAAA,iBAAiB,EAAEJ;AAHrB,KAIGoC,WAAW,EAJd,CALF,CAhFF,CADF;AA+FD,CA9KD;;AAsLA,SAASsC,oBAAT,CAA8B;AAC5BC,EAAAA,KAD4B;AAE5B1C,EAAAA,UAF4B;AAG5BF,EAAAA;AAH4B,CAA9B,EAIuB;AACrB,QAAM;AAAEM,IAAAA,GAAF;AAAOuC,IAAAA;AAAP,MAAkBD,KAAxB;AAEA,sBACE,oBAAC,+BAAD;AAAa,IAAA,KAAK,EAAEvD,MAAM,CAACC;AAA3B,KACGuD,MAAM,CAACC,GAAP,CAAW,CAACzE,KAAD,EAAQ4B,KAAR,kBACV,oBAAC,SAAD;AACE,IAAA,GAAG,EAAE5B,KAAK,CAACiC,GADb;AAEE,IAAA,WAAW,EAAEN,WAFf;AAGE,IAAA,KAAK,EAAE3B,KAHT;AAIE,IAAA,KAAK,EAAE4B,KAJT;AAKE,IAAA,UAAU,EAAEC,UALd;AAME,IAAA,QAAQ,EAAEI;AANZ,IADD,CADH,CADF;AAcD;;AAEc,SAASyC,eAAT,CAAyB/E,KAAzB,EAAuC;AACpD,sBACE,oBAAC,+BAAD,qBACE,oBAAC,oBAAD,EAA0BA,KAA1B,CADF,CADF;AAKD;;AAED,MAAMqB,MAAM,GAAGQ,wBAAWmD,MAAX,CAAkB;AAC/B1D,EAAAA,SAAS,EAAE;AACT2D,IAAAA,IAAI,EAAE;AADG;AADoB,CAAlB,CAAf", "sourcesContent": ["import * as React from 'react';\nimport { Platform, StyleSheet, View, ViewProps } from 'react-native';\n// @ts-ignore Getting private component\nimport AppContainer from 'react-native/Libraries/ReactNative/AppContainer';\nimport warnOnce from 'warn-once';\nimport {\n  ScreenStack,\n  StackPresentationTypes,\n  ScreenContext,\n} from 'react-native-screens';\nimport {\n  ParamListBase,\n  StackActions,\n  StackNavigationState,\n  useTheme,\n  Route,\n  NavigationState,\n  PartialState,\n} from '@react-navigation/native';\nimport {\n  useSafeAreaFrame,\n  useSafeAreaInsets,\n} from 'react-native-safe-area-context';\n\nimport {\n  NativeStackDescriptorMap,\n  NativeStackNavigationHelpers,\n  NativeStackNavigationOptions,\n} from '../types';\nimport HeaderConfig from './HeaderConfig';\nimport SafeAreaProviderCompat from '../utils/SafeAreaProviderCompat';\nimport getDefaultHeaderHeight from '../utils/getDefaultHeaderHeight';\nimport HeaderHeightContext from '../utils/HeaderHeightContext';\n\nconst isAndroid = Platform.OS === 'android';\n\nlet Container = View;\n\nif (__DEV__) {\n  const DebugContainer = (\n    props: ViewProps & { stackPresentation: StackPresentationTypes }\n  ) => {\n    const { stackPresentation, ...rest } = props;\n    if (Platform.OS === 'ios' && stackPresentation !== 'push') {\n      return (\n        <AppContainer>\n          <View {...rest} />\n        </AppContainer>\n      );\n    }\n    return <View {...rest} />;\n  };\n  // @ts-ignore Wrong props\n  Container = DebugContainer;\n}\n\nconst MaybeNestedStack = ({\n  options,\n  route,\n  stackPresentation,\n  children,\n}: {\n  options: NativeStackNavigationOptions;\n  route: Route<string>;\n  stackPresentation: StackPresentationTypes;\n  children: React.ReactNode;\n}) => {\n  const { colors } = useTheme();\n  const { headerShown = true, contentStyle } = options;\n\n  const Screen = React.useContext(ScreenContext);\n\n  const isHeaderInModal = isAndroid\n    ? false\n    : stackPresentation !== 'push' && headerShown === true;\n\n  const headerShownPreviousRef = React.useRef(headerShown);\n\n  React.useEffect(() => {\n    warnOnce(\n      !isAndroid &&\n        stackPresentation !== 'push' &&\n        headerShownPreviousRef.current !== headerShown,\n      `Dynamically changing 'headerShown' in modals will result in remounting the screen and losing all local state. See options for the screen '${route.name}'.`\n    );\n\n    headerShownPreviousRef.current = headerShown;\n  }, [headerShown, stackPresentation, route.name]);\n\n  const content = (\n    <Container\n      style={[\n        styles.container,\n        stackPresentation !== 'transparentModal' &&\n          stackPresentation !== 'containedTransparentModal' && {\n            backgroundColor: colors.background,\n          },\n        contentStyle,\n      ]}\n      // @ts-ignore Wrong props passed to View\n      stackPresentation={stackPresentation}>\n      {children}\n    </Container>\n  );\n\n  const topInset = useSafeAreaInsets().top;\n  const dimensions = useSafeAreaFrame();\n  const headerHeight = getDefaultHeaderHeight(\n    dimensions,\n    topInset,\n    stackPresentation\n  );\n\n  if (isHeaderInModal) {\n    return (\n      <ScreenStack style={styles.container}>\n        <Screen enabled isNativeStack style={StyleSheet.absoluteFill}>\n          <HeaderHeightContext.Provider value={headerHeight}>\n            <HeaderConfig {...options} route={route} />\n            {content}\n          </HeaderHeightContext.Provider>\n        </Screen>\n      </ScreenStack>\n    );\n  }\n\n  return content;\n};\n\ntype NavigationRoute<\n  ParamList extends ParamListBase,\n  RouteName extends keyof ParamList\n> = Route<Extract<RouteName, string>, ParamList[RouteName]> & {\n  state?: NavigationState | PartialState<NavigationState>;\n};\n\nconst RouteView = ({\n  descriptors,\n  route,\n  index,\n  navigation,\n  stateKey,\n}: {\n  descriptors: NativeStackDescriptorMap;\n  route: NavigationRoute<ParamListBase, string>;\n  index: number;\n  navigation: NativeStackNavigationHelpers;\n  stateKey: string;\n}) => {\n  const { options, render: renderScene } = descriptors[route.key];\n  const {\n    gestureEnabled,\n    headerShown,\n    hideKeyboardOnSwipe,\n    homeIndicatorHidden,\n    nativeBackButtonDismissalEnabled = false,\n    navigationBarColor,\n    navigationBarHidden,\n    replaceAnimation = 'pop',\n    screenOrientation,\n    statusBarAnimation,\n    statusBarColor,\n    statusBarHidden,\n    statusBarStyle,\n    statusBarTranslucent,\n    swipeDirection = 'horizontal',\n    transitionDuration,\n    freezeOnBlur,\n  } = options;\n\n  let {\n    customAnimationOnSwipe,\n    fullScreenSwipeEnabled,\n    gestureResponseDistance,\n    stackAnimation,\n    stackPresentation = 'push',\n  } = options;\n\n  if (swipeDirection === 'vertical') {\n    // for `vertical` direction to work, we need to set `fullScreenSwipeEnabled` to `true`\n    // so the screen can be dismissed from any point on screen.\n    // `customAnimationOnSwipe` needs to be set to `true` so the `stackAnimation` set by user can be used,\n    // otherwise `simple_push` will be used.\n    // Also, the default animation for this direction seems to be `slide_from_bottom`.\n    if (fullScreenSwipeEnabled === undefined) {\n      fullScreenSwipeEnabled = true;\n    }\n    if (customAnimationOnSwipe === undefined) {\n      customAnimationOnSwipe = true;\n    }\n    if (stackAnimation === undefined) {\n      stackAnimation = 'slide_from_bottom';\n    }\n  }\n\n  if (index === 0) {\n    // first screen should always be treated as `push`, it resolves problems with no header animation\n    // for navigator with first screen as `modal` and the next as `push`\n    stackPresentation = 'push';\n  }\n\n  const isHeaderInPush = isAndroid\n    ? headerShown\n    : stackPresentation === 'push' && headerShown !== false;\n\n  const dimensions = useSafeAreaFrame();\n  const topInset = useSafeAreaInsets().top;\n  const headerHeight = getDefaultHeaderHeight(\n    dimensions,\n    topInset,\n    stackPresentation\n  );\n  const parentHeaderHeight = React.useContext(HeaderHeightContext);\n  const Screen = React.useContext(ScreenContext);\n\n  return (\n    <Screen\n      key={route.key}\n      enabled\n      isNativeStack\n      style={StyleSheet.absoluteFill}\n      customAnimationOnSwipe={customAnimationOnSwipe}\n      freezeOnBlur={freezeOnBlur}\n      fullScreenSwipeEnabled={fullScreenSwipeEnabled}\n      hideKeyboardOnSwipe={hideKeyboardOnSwipe}\n      homeIndicatorHidden={homeIndicatorHidden}\n      gestureEnabled={isAndroid ? false : gestureEnabled}\n      gestureResponseDistance={gestureResponseDistance}\n      nativeBackButtonDismissalEnabled={nativeBackButtonDismissalEnabled}\n      navigationBarColor={navigationBarColor}\n      navigationBarHidden={navigationBarHidden}\n      replaceAnimation={replaceAnimation}\n      screenOrientation={screenOrientation}\n      stackAnimation={stackAnimation}\n      stackPresentation={stackPresentation}\n      statusBarAnimation={statusBarAnimation}\n      statusBarColor={statusBarColor}\n      statusBarHidden={statusBarHidden}\n      statusBarStyle={statusBarStyle}\n      statusBarTranslucent={statusBarTranslucent}\n      swipeDirection={swipeDirection}\n      transitionDuration={transitionDuration}\n      onHeaderBackButtonClicked={() => {\n        navigation.dispatch({\n          ...StackActions.pop(),\n          source: route.key,\n          target: stateKey,\n        });\n      }}\n      onWillAppear={() => {\n        navigation.emit({\n          type: 'transitionStart',\n          data: { closing: false },\n          target: route.key,\n        });\n      }}\n      onWillDisappear={() => {\n        navigation.emit({\n          type: 'transitionStart',\n          data: { closing: true },\n          target: route.key,\n        });\n      }}\n      onAppear={() => {\n        navigation.emit({\n          type: 'appear',\n          target: route.key,\n        });\n        navigation.emit({\n          type: 'transitionEnd',\n          data: { closing: false },\n          target: route.key,\n        });\n      }}\n      onDisappear={() => {\n        navigation.emit({\n          type: 'transitionEnd',\n          data: { closing: true },\n          target: route.key,\n        });\n      }}\n      onDismissed={(e) => {\n        navigation.emit({\n          type: 'dismiss',\n          target: route.key,\n        });\n\n        const dismissCount =\n          e.nativeEvent.dismissCount > 0 ? e.nativeEvent.dismissCount : 1;\n\n        navigation.dispatch({\n          ...StackActions.pop(dismissCount),\n          source: route.key,\n          target: stateKey,\n        });\n      }}>\n      <HeaderHeightContext.Provider\n        value={\n          isHeaderInPush !== false ? headerHeight : parentHeaderHeight ?? 0\n        }>\n        <HeaderConfig {...options} route={route} headerShown={isHeaderInPush} />\n        <MaybeNestedStack\n          options={options}\n          route={route}\n          stackPresentation={stackPresentation}>\n          {renderScene()}\n        </MaybeNestedStack>\n      </HeaderHeightContext.Provider>\n    </Screen>\n  );\n};\n\ntype Props = {\n  state: StackNavigationState<ParamListBase>;\n  navigation: NativeStackNavigationHelpers;\n  descriptors: NativeStackDescriptorMap;\n};\n\nfunction NativeStackViewInner({\n  state,\n  navigation,\n  descriptors,\n}: Props): JSX.Element {\n  const { key, routes } = state;\n\n  return (\n    <ScreenStack style={styles.container}>\n      {routes.map((route, index) => (\n        <RouteView\n          key={route.key}\n          descriptors={descriptors}\n          route={route}\n          index={index}\n          navigation={navigation}\n          stateKey={key}\n        />\n      ))}\n    </ScreenStack>\n  );\n}\n\nexport default function NativeStackView(props: Props) {\n  return (\n    <SafeAreaProviderCompat>\n      <NativeStackViewInner {...props} />\n    </SafeAreaProviderCompat>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n});\n"]}
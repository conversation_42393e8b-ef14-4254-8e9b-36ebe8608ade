{"version": 3, "sources": ["FontProcessor.tsx"], "names": ["processFonts", "fontFamilies", "fontFamilyProcessor", "ReactNativeStyleAttributes", "fontFamily", "process", "map"], "mappings": ";;;;;;;AACA;;;;AADA;AAGO,SAASA,YAAT,CACLC,YADK,EAEmB;AAAA;;AACxB;AACA,QAAMC,mBAAmB,4BAAGC,oCAA2BC,UAA9B,0DAAG,sBAAuCC,OAAnE;;AACA,MAAI,OAAOH,mBAAP,KAA+B,UAAnC,EAA+C;AAC7C,WAAOD,YAAY,CAACK,GAAb,CAAiBJ,mBAAjB,CAAP;AACD;;AACD,SAAOD,YAAP;AACD", "sourcesContent": ["// @ts-ignore: No declaration available\nimport ReactNativeStyleAttributes from 'react-native/Libraries/Components/View/ReactNativeStyleAttributes';\n\nexport function processFonts(\n  fontFamilies: (string | undefined)[]\n): (string | undefined)[] {\n  // @ts-ignore: React Native types are incorrect here and don't consider fontFamily a style value\n  const fontFamilyProcessor = ReactNativeStyleAttributes.fontFamily?.process;\n  if (typeof fontFamilyProcessor === 'function') {\n    return fontFamilies.map(fontFamilyProcessor);\n  }\n  return fontFamilies;\n}\n"]}
{"version": 3, "sources": ["HeaderConfig.tsx"], "names": ["HeaderConfig", "backButtonImage", "backButtonInCustomView", "direction", "disableBackButtonMenu", "headerBackTitle", "headerBackTitleStyle", "headerBackTitleVisible", "headerCenter", "headerHideBackButton", "headerHideShadow", "headerLargeStyle", "headerLargeTitle", "headerLargeTitleHideShadow", "headerLargeTitleStyle", "headerLeft", "headerRight", "headerShown", "headerStyle", "headerTintColor", "headerTitle", "headerTitleStyle", "headerTopInsetEnabled", "headerTranslucent", "route", "searchBar", "title", "colors", "tintColor", "primary", "handleAttached", "handleDetached", "clearSubscription", "createSubscription", "onBackPress", "executeNativeBackPress", "isDisabled", "disableBackButtonOverride", "backTitleFontFamily", "largeTitleFontFamily", "titleFontFamily", "fontFamily", "React", "useEffect", "processedSearchBarOptions", "useMemo", "Platform", "OS", "onFocus", "args", "onClose", "backgroundColor", "card", "fontSize", "blurEffect", "color", "fontWeight", "undefined", "name", "text", "isSearchBarAvailableForCurrentPlatform"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AACA;;AAaA;;AACA;;;;;;AAMe,SAASA,YAAT,CAAsB;AACnCC,EAAAA,eADmC;AAEnCC,EAAAA,sBAFmC;AAGnCC,EAAAA,SAHmC;AAInCC,EAAAA,qBAJmC;AAKnCC,EAAAA,eALmC;AAMnCC,EAAAA,oBAAoB,GAAG,EANY;AAOnCC,EAAAA,sBAAsB,GAAG,IAPU;AAQnCC,EAAAA,YARmC;AASnCC,EAAAA,oBATmC;AAUnCC,EAAAA,gBAVmC;AAWnCC,EAAAA,gBAAgB,GAAG,EAXgB;AAYnCC,EAAAA,gBAZmC;AAanCC,EAAAA,0BAbmC;AAcnCC,EAAAA,qBAAqB,GAAG,EAdW;AAenCC,EAAAA,UAfmC;AAgBnCC,EAAAA,WAhBmC;AAiBnCC,EAAAA,WAjBmC;AAkBnCC,EAAAA,WAAW,GAAG,EAlBqB;AAmBnCC,EAAAA,eAnBmC;AAoBnCC,EAAAA,WApBmC;AAqBnCC,EAAAA,gBAAgB,GAAG,EArBgB;AAsBnCC,EAAAA,qBAAqB,GAAG,IAtBW;AAuBnCC,EAAAA,iBAvBmC;AAwBnCC,EAAAA,KAxBmC;AAyBnCC,EAAAA,SAzBmC;AA0BnCC,EAAAA;AA1BmC,CAAtB,EA2BQ;AACrB,QAAM;AAAEC,IAAAA;AAAF,MAAa,uBAAnB;AACA,QAAMC,SAAS,GAAGT,eAAH,aAAGA,eAAH,cAAGA,eAAH,GAAsBQ,MAAM,CAACE,OAA5C,CAFqB,CAIrB;AACA;AACA;;AACA,QAAM;AACJC,IAAAA,cADI;AAEJC,IAAAA,cAFI;AAGJC,IAAAA,iBAHI;AAIJC,IAAAA;AAJI,MAKF,wDAAyB;AAC3BC,IAAAA,WAAW,EAAEC,0CADc;AAE3BC,IAAAA,UAAU,EAAE,CAACX,SAAD,IAAc,CAAC,CAACA,SAAS,CAACY;AAFX,GAAzB,CALJ;AAUA,QAAM,CACJC,mBADI,EAEJC,oBAFI,EAGJC,eAHI,IAIF,iCAAa,CACflC,oBAAoB,CAACmC,UADN,EAEf3B,qBAAqB,CAAC2B,UAFP,EAGfpB,gBAAgB,CAACoB,UAHF,CAAb,CAJJ,CAjBqB,CA2BrB;;AACAC,EAAAA,KAAK,CAACC,SAAN,CAAgB,MAAMX,iBAAtB,EAAyC,CAACP,SAAD,CAAzC;AAEA,QAAMmB,yBAAyB,GAAGF,KAAK,CAACG,OAAN,CAAc,MAAM;AACpD,QACEC,sBAASC,EAAT,KAAgB,SAAhB,IACAtB,SADA,IAEA,CAACA,SAAS,CAACY,yBAHb,EAIE;AACA,YAAMW,OAAkC,GAAG,CAAC,GAAGC,IAAJ,KAAa;AAAA;;AACtDhB,QAAAA,kBAAkB;AAClB,8BAAAR,SAAS,CAACuB,OAAV,+EAAAvB,SAAS,EAAW,GAAGwB,IAAd,CAAT;AACD,OAHD;;AAIA,YAAMC,OAAkC,GAAG,CAAC,GAAGD,IAAJ,KAAa;AAAA;;AACtDjB,QAAAA,iBAAiB;AACjB,8BAAAP,SAAS,CAACyB,OAAV,+EAAAzB,SAAS,EAAW,GAAGwB,IAAd,CAAT;AACD,OAHD;;AAKA,aAAO,EAAE,GAAGxB,SAAL;AAAgBuB,QAAAA,OAAhB;AAAyBE,QAAAA;AAAzB,OAAP;AACD;;AACD,WAAOzB,SAAP;AACD,GAlBiC,EAkB/B,CAACA,SAAD,EAAYQ,kBAAZ,EAAgCD,iBAAhC,CAlB+B,CAAlC;AAoBA,sBACE,oBAAC,2CAAD;AACE,IAAA,sBAAsB,EAAE9B,sBAD1B;AAEE,IAAA,eAAe,EACbgB,WAAW,CAACiC,eAAZ,GAA8BjC,WAAW,CAACiC,eAA1C,GAA4DxB,MAAM,CAACyB,IAHvE;AAKE,IAAA,SAAS,EAAE7C,sBAAsB,GAAGF,eAAH,GAAqB,GALxD;AAME,IAAA,mBAAmB,EAAEiC,mBANvB;AAOE,IAAA,iBAAiB,EAAEhC,oBAAoB,CAAC+C,QAP1C;AAQE,IAAA,UAAU,EAAEnC,WAAW,CAACoC,UAR1B;AASE,IAAA,KAAK,EAAE1B,SATT;AAUE,IAAA,SAAS,EAAEzB,SAVb;AAWE,IAAA,qBAAqB,EAAEC,qBAXzB;AAYE,IAAA,MAAM,EAAEa,WAAW,KAAK,KAZ1B;AAaE,IAAA,cAAc,EAAER,oBAblB;AAcE,IAAA,UAAU,EAAEC,gBAdd;AAeE,IAAA,UAAU,EAAEE,gBAfd;AAgBE,IAAA,yBAAyB,EAAED,gBAAgB,CAACwC,eAhB9C;AAiBE,IAAA,eAAe,EAAErC,qBAAqB,CAACyC,KAjBzC;AAkBE,IAAA,oBAAoB,EAAEhB,oBAlBxB;AAmBE,IAAA,kBAAkB,EAAEzB,qBAAqB,CAACuC,QAnB5C;AAoBE,IAAA,oBAAoB,EAAEvC,qBAAqB,CAAC0C,UApB9C;AAqBE,IAAA,oBAAoB,EAAE3C,0BArBxB;AAsBE,IAAA,KAAK,EACHO,WAAW,KAAKqC,SAAhB,GACIrC,WADJ,GAEIM,KAAK,KAAK+B,SAAV,GACA/B,KADA,GAEAF,KAAK,CAACkC,IA3Bd;AA6BE,IAAA,UAAU,EACRrC,gBAAgB,CAACkC,KAAjB,KAA2BE,SAA3B,GACIpC,gBAAgB,CAACkC,KADrB,GAEIpC,eAAe,KAAKsC,SAApB,GACAtC,eADA,GAEAQ,MAAM,CAACgC,IAlCf;AAoCE,IAAA,eAAe,EAAEnB,eApCnB;AAqCE,IAAA,aAAa,EAAEnB,gBAAgB,CAACgC,QArClC;AAsCE,IAAA,eAAe,EAAEhC,gBAAgB,CAACmC,UAtCpC;AAuCE,IAAA,eAAe,EAAElC,qBAvCnB;AAwCE,IAAA,WAAW,EAAEC,iBAAiB,KAAK,IAxCrC;AAyCE,IAAA,UAAU,EAAEO,cAzCd;AA0CE,IAAA,UAAU,EAAEC;AA1Cd,KA2CGf,WAAW,KAAKyC,SAAhB,gBACC,oBAAC,8CAAD,QACGzC,WAAW,CAAC;AAAEY,IAAAA;AAAF,GAAD,CADd,CADD,GAIG,IA/CN,EAgDG3B,eAAe,KAAKwD,SAApB,gBACC,oBAAC,oDAAD;AACE,IAAA,GAAG,EAAC,WADN;AAEE,IAAA,MAAM,EAAExD;AAFV,IADD,GAKG,IArDN,EAsDGc,UAAU,KAAK0C,SAAf,gBACC,oBAAC,6CAAD,QACG1C,UAAU,CAAC;AAAEa,IAAAA;AAAF,GAAD,CADb,CADD,GAIG,IA1DN,EA2DGpB,YAAY,KAAKiD,SAAjB,gBACC,oBAAC,+CAAD,QACGjD,YAAY,CAAC;AAAEoB,IAAAA;AAAF,GAAD,CADf,CADD,GAIG,IA/DN,EAgEGgC,8DACDhB,yBAAyB,KAAKa,SAD7B,gBAEC,oBAAC,kDAAD,qBACE,oBAAC,6BAAD,EAAeb,yBAAf,CADF,CAFD,GAKG,IArEN,CADF;AAyED", "sourcesContent": ["import { Route, useTheme } from '@react-navigation/native';\nimport * as React from 'react';\nimport { Platform } from 'react-native';\nimport {\n  ScreenStackHeaderBackButtonImage,\n  ScreenStackHeaderCenterView,\n  ScreenStackHeaderConfig,\n  ScreenStackHeaderLeftView,\n  ScreenStackHeaderRightView,\n  ScreenStackHeaderSearchBarView,\n  SearchBar,\n  SearchBarProps,\n  isSearchBarAvailableForCurrentPlatform,\n  executeNativeBackPress,\n} from 'react-native-screens';\nimport { NativeStackNavigationOptions } from '../types';\nimport { useBackPressSubscription } from '../utils/useBackPressSubscription';\nimport { processFonts } from './FontProcessor';\n\ntype Props = NativeStackNavigationOptions & {\n  route: Route<string>;\n};\n\nexport default function HeaderConfig({\n  backButtonImage,\n  backButtonInCustomView,\n  direction,\n  disableBackButtonMenu,\n  headerBackTitle,\n  headerBackTitleStyle = {},\n  headerBackTitleVisible = true,\n  headerCenter,\n  headerHideBackButton,\n  headerHideShadow,\n  headerLargeStyle = {},\n  headerLargeTitle,\n  headerLargeTitleHideShadow,\n  headerLargeTitleStyle = {},\n  headerLeft,\n  headerRight,\n  headerShown,\n  headerStyle = {},\n  headerTintColor,\n  headerTitle,\n  headerTitleStyle = {},\n  headerTopInsetEnabled = true,\n  headerTranslucent,\n  route,\n  searchBar,\n  title,\n}: Props): JSX.Element {\n  const { colors } = useTheme();\n  const tintColor = headerTintColor ?? colors.primary;\n\n  // We need to use back press subscription here to override back button behavior on JS side.\n  // Because screens are usually used with react-navigation and this library overrides back button\n  // we need to handle it first in case when search bar is open\n  const {\n    handleAttached,\n    handleDetached,\n    clearSubscription,\n    createSubscription,\n  } = useBackPressSubscription({\n    onBackPress: executeNativeBackPress,\n    isDisabled: !searchBar || !!searchBar.disableBackButtonOverride,\n  });\n\n  const [\n    backTitleFontFamily,\n    largeTitleFontFamily,\n    titleFontFamily,\n  ] = processFonts([\n    headerBackTitleStyle.fontFamily,\n    headerLargeTitleStyle.fontFamily,\n    headerTitleStyle.fontFamily,\n  ]);\n\n  // We want to clear clearSubscription only when components unmounts or search bar changes\n  React.useEffect(() => clearSubscription, [searchBar]);\n\n  const processedSearchBarOptions = React.useMemo(() => {\n    if (\n      Platform.OS === 'android' &&\n      searchBar &&\n      !searchBar.disableBackButtonOverride\n    ) {\n      const onFocus: SearchBarProps['onFocus'] = (...args) => {\n        createSubscription();\n        searchBar.onFocus?.(...args);\n      };\n      const onClose: SearchBarProps['onClose'] = (...args) => {\n        clearSubscription();\n        searchBar.onClose?.(...args);\n      };\n\n      return { ...searchBar, onFocus, onClose };\n    }\n    return searchBar;\n  }, [searchBar, createSubscription, clearSubscription]);\n\n  return (\n    <ScreenStackHeaderConfig\n      backButtonInCustomView={backButtonInCustomView}\n      backgroundColor={\n        headerStyle.backgroundColor ? headerStyle.backgroundColor : colors.card\n      }\n      backTitle={headerBackTitleVisible ? headerBackTitle : ' '}\n      backTitleFontFamily={backTitleFontFamily}\n      backTitleFontSize={headerBackTitleStyle.fontSize}\n      blurEffect={headerStyle.blurEffect}\n      color={tintColor}\n      direction={direction}\n      disableBackButtonMenu={disableBackButtonMenu}\n      hidden={headerShown === false}\n      hideBackButton={headerHideBackButton}\n      hideShadow={headerHideShadow}\n      largeTitle={headerLargeTitle}\n      largeTitleBackgroundColor={headerLargeStyle.backgroundColor}\n      largeTitleColor={headerLargeTitleStyle.color}\n      largeTitleFontFamily={largeTitleFontFamily}\n      largeTitleFontSize={headerLargeTitleStyle.fontSize}\n      largeTitleFontWeight={headerLargeTitleStyle.fontWeight}\n      largeTitleHideShadow={headerLargeTitleHideShadow}\n      title={\n        headerTitle !== undefined\n          ? headerTitle\n          : title !== undefined\n          ? title\n          : route.name\n      }\n      titleColor={\n        headerTitleStyle.color !== undefined\n          ? headerTitleStyle.color\n          : headerTintColor !== undefined\n          ? headerTintColor\n          : colors.text\n      }\n      titleFontFamily={titleFontFamily}\n      titleFontSize={headerTitleStyle.fontSize}\n      titleFontWeight={headerTitleStyle.fontWeight}\n      topInsetEnabled={headerTopInsetEnabled}\n      translucent={headerTranslucent === true}\n      onAttached={handleAttached}\n      onDetached={handleDetached}>\n      {headerRight !== undefined ? (\n        <ScreenStackHeaderRightView>\n          {headerRight({ tintColor })}\n        </ScreenStackHeaderRightView>\n      ) : null}\n      {backButtonImage !== undefined ? (\n        <ScreenStackHeaderBackButtonImage\n          key=\"backImage\"\n          source={backButtonImage}\n        />\n      ) : null}\n      {headerLeft !== undefined ? (\n        <ScreenStackHeaderLeftView>\n          {headerLeft({ tintColor })}\n        </ScreenStackHeaderLeftView>\n      ) : null}\n      {headerCenter !== undefined ? (\n        <ScreenStackHeaderCenterView>\n          {headerCenter({ tintColor })}\n        </ScreenStackHeaderCenterView>\n      ) : null}\n      {isSearchBarAvailableForCurrentPlatform &&\n      processedSearchBarOptions !== undefined ? (\n        <ScreenStackHeaderSearchBarView>\n          <SearchBar {...processedSearchBarOptions} />\n        </ScreenStackHeaderSearchBarView>\n      ) : null}\n    </ScreenStackHeaderConfig>\n  );\n}\n"]}
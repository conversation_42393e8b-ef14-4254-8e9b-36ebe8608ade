{"version": 3, "sources": ["getDefaultHeaderHeight.tsx"], "names": ["formSheetModalHeight", "getDefaultHeaderHeight", "layout", "topInset", "stackPresentation", "headerHeight", "Platform", "OS", "statusBarHeight", "isLandscape", "width", "height", "isFromSheetModal", "isPad", "isTV"], "mappings": ";;;;;;;AAAA;;AAIA,MAAMA,oBAAoB,GAAG,EAA7B;;AAEe,SAASC,sBAAT,CACbC,MADa,EAEbC,QAFa,EAGbC,iBAHa,EAIL;AACR;AACA,MAAIC,YAAY,GAAGC,sBAASC,EAAT,KAAgB,SAAhB,GAA4B,EAA5B,GAAiC,EAApD;AACA,MAAIC,eAAe,GAAGL,QAAtB;;AAEA,MAAIG,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzB,UAAME,WAAW,GAAGP,MAAM,CAACQ,KAAP,GAAeR,MAAM,CAACS,MAA1C;AACA,UAAMC,gBAAgB,GACpBR,iBAAiB,KAAK,OAAtB,IAAiCA,iBAAiB,KAAK,WADzD;;AAEA,QAAIQ,gBAAgB,IAAI,CAACH,WAAzB,EAAsC;AACpC;AACAD,MAAAA,eAAe,GAAG,CAAlB;AACD;;AAED,QAAIF,sBAASO,KAAT,IAAkBP,sBAASQ,IAA/B,EAAqC;AACnCT,MAAAA,YAAY,GAAGO,gBAAgB,GAAGZ,oBAAH,GAA0B,EAAzD;AACD,KAFD,MAEO;AACL,UAAIS,WAAJ,EAAiB;AACfJ,QAAAA,YAAY,GAAG,EAAf;AACD,OAFD,MAEO;AACLA,QAAAA,YAAY,GAAGO,gBAAgB,GAAGZ,oBAAH,GAA0B,EAAzD;AACD;AACF;AACF;;AAED,SAAOK,YAAY,GAAGG,eAAtB;AACD", "sourcesContent": ["import { Platform } from 'react-native';\nimport { StackPresentationTypes } from 'react-native-screens';\ntype Layout = { width: number; height: number };\n\nconst formSheetModalHeight = 56;\n\nexport default function getDefaultHeaderHeight(\n  layout: Layout,\n  topInset: number,\n  stackPresentation: StackPresentationTypes\n): number {\n  // default header heights\n  let headerHeight = Platform.OS === 'android' ? 56 : 64;\n  let statusBarHeight = topInset;\n\n  if (Platform.OS === 'ios') {\n    const isLandscape = layout.width > layout.height;\n    const isFromSheetModal =\n      stackPresentation === 'modal' || stackPresentation === 'formSheet';\n    if (isFromSheetModal && !isLandscape) {\n      // `modal` and `formSheet` presentations do not take whole screen, so should not take the inset.\n      statusBarHeight = 0;\n    }\n\n    if (Platform.isPad || Platform.isTV) {\n      headerHeight = isFromSheetModal ? formSheetModalHeight : 50;\n    } else {\n      if (isLandscape) {\n        headerHeight = 32;\n      } else {\n        headerHeight = isFromSheetModal ? formSheetModalHeight : 44;\n      }\n    }\n  }\n\n  return headerHeight + statusBarHeight;\n}\n"]}
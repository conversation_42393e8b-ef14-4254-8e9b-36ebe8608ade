{"version": 3, "sources": ["useHeaderHeight.tsx"], "names": ["useHeaderHeight", "height", "React", "useContext", "HeaderHeightContext", "undefined", "Error"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;;;;AAEe,SAASA,eAAT,GAA2B;AACxC,QAAMC,MAAM,GAAGC,KAAK,CAACC,UAAN,CAAiBC,4BAAjB,CAAf;;AAEA,MAAIH,MAAM,KAAKI,SAAf,EAA0B;AACxB,UAAM,IAAIC,KAAJ,CACJ,wFADI,CAAN;AAGD;;AAED,SAAOL,MAAP;AACD", "sourcesContent": ["import * as React from 'react';\n\nimport HeaderHeightContext from './HeaderHeightContext';\n\nexport default function useHeaderHeight() {\n  const height = React.useContext(HeaderHeightContext);\n\n  if (height === undefined) {\n    throw new Error(\n      \"Couldn't find the header height. Are you inside a screen in a navigator with a header?\"\n    );\n  }\n\n  return height;\n}\n"]}
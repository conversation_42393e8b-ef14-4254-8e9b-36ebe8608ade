{"version": 3, "sources": ["HeaderHeightContext.tsx"], "names": ["HeaderHeightContext", "React", "createContext", "undefined"], "mappings": ";;;;;;;AAAA;;;;;;AAEA,MAAMA,mBAAmB,gBAAGC,KAAK,CAACC,aAAN,CAAwCC,SAAxC,CAA5B;eAEeH,mB", "sourcesContent": ["import * as React from 'react';\n\nconst HeaderHeightContext = React.createContext<number | undefined>(undefined);\n\nexport default HeaderHeightContext;\n"]}
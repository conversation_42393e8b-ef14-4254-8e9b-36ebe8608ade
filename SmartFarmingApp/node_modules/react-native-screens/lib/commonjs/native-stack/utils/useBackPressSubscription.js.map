{"version": 3, "sources": ["useBackPressSubscription.tsx"], "names": ["useBackPressSubscription", "onBackPress", "isDisabled", "isActive", "setIsActive", "React", "useState", "subscription", "useRef", "clearSubscription", "useCallback", "shouldSetActive", "current", "remove", "undefined", "createSubscription", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "handleAttached", "handleDetached", "useEffect"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAcA;AACA;AACA;AACA;AACO,SAASA,wBAAT,CAAkC;AACvCC,EAAAA,WADuC;AAEvCC,EAAAA;AAFuC,CAAlC,EAG4B;AACjC,QAAM,CAACC,QAAD,EAAWC,WAAX,IAA0BC,eAAMC,QAAN,CAAe,KAAf,CAAhC;;AACA,QAAMC,YAAY,GAAGF,eAAMG,MAAN,EAArB;;AAEA,QAAMC,iBAAiB,GAAGJ,eAAMK,WAAN,CAAkB,CAACC,eAAe,GAAG,IAAnB,KAA4B;AAAA;;AACtE,6BAAAJ,YAAY,CAACK,OAAb,gFAAsBC,MAAtB;AACAN,IAAAA,YAAY,CAACK,OAAb,GAAuBE,SAAvB;AACA,QAAIH,eAAJ,EAAqBP,WAAW,CAAC,KAAD,CAAX;AACtB,GAJyB,EAIvB,EAJuB,CAA1B;;AAMA,QAAMW,kBAAkB,GAAGV,eAAMK,WAAN,CAAkB,MAAM;AACjD,QAAI,CAACR,UAAL,EAAiB;AAAA;;AACf,gCAAAK,YAAY,CAACK,OAAb,kFAAsBC,MAAtB;AACAN,MAAAA,YAAY,CAACK,OAAb,GAAuBI,yBAAYC,gBAAZ,CACrB,mBADqB,EAErBhB,WAFqB,CAAvB;AAIAG,MAAAA,WAAW,CAAC,IAAD,CAAX;AACD;AACF,GAT0B,EASxB,CAACF,UAAD,EAAaD,WAAb,CATwB,CAA3B;;AAWA,QAAMiB,cAAc,GAAGb,eAAMK,WAAN,CAAkB,MAAM;AAC7C,QAAIP,QAAJ,EAAc;AACZY,MAAAA,kBAAkB;AACnB;AACF,GAJsB,EAIpB,CAACA,kBAAD,EAAqBZ,QAArB,CAJoB,CAAvB;;AAMA,QAAMgB,cAAc,GAAGd,eAAMK,WAAN,CAAkB,MAAM;AAC7CD,IAAAA,iBAAiB,CAAC,KAAD,CAAjB;AACD,GAFsB,EAEpB,CAACA,iBAAD,CAFoB,CAAvB;;AAIAJ,iBAAMe,SAAN,CAAgB,MAAM;AACpB,QAAIlB,UAAJ,EAAgB;AACdO,MAAAA,iBAAiB;AAClB;AACF,GAJD,EAIG,CAACP,UAAD,EAAaO,iBAAb,CAJH;;AAMA,SAAO;AACLS,IAAAA,cADK;AAELC,IAAAA,cAFK;AAGLJ,IAAAA,kBAHK;AAILN,IAAAA;AAJK,GAAP;AAMD", "sourcesContent": ["import React from 'react';\nimport { BackHandler, NativeEventSubscription } from 'react-native';\n\ninterface Args {\n  onBackPress: () => boolean;\n  isDisabled: boolean;\n}\n\ninterface UseBackPressSubscription {\n  handleAttached: () => void;\n  handleDetached: () => void;\n  createSubscription: () => void;\n  clearSubscription: () => void;\n}\n\n/**\n * This hook is an abstraction for keeping back press subscription\n * logic in one place.\n */\nexport function useBackPressSubscription({\n  onBackPress,\n  isDisabled,\n}: Args): UseBackPressSubscription {\n  const [isActive, setIsActive] = React.useState(false);\n  const subscription = React.useRef<NativeEventSubscription | undefined>();\n\n  const clearSubscription = React.useCallback((shouldSetActive = true) => {\n    subscription.current?.remove();\n    subscription.current = undefined;\n    if (shouldSetActive) setIsActive(false);\n  }, []);\n\n  const createSubscription = React.useCallback(() => {\n    if (!isDisabled) {\n      subscription.current?.remove();\n      subscription.current = BackHandler.addEventListener(\n        'hardwareBackPress',\n        onBackPress\n      );\n      setIsActive(true);\n    }\n  }, [isDisabled, onBackPress]);\n\n  const handleAttached = React.useCallback(() => {\n    if (isActive) {\n      createSubscription();\n    }\n  }, [createSubscription, isActive]);\n\n  const handleDetached = React.useCallback(() => {\n    clearSubscription(false);\n  }, [clearSubscription]);\n\n  React.useEffect(() => {\n    if (isDisabled) {\n      clearSubscription();\n    }\n  }, [isDisabled, clearSubscription]);\n\n  return {\n    handleAttached,\n    handleDetached,\n    createSubscription,\n    clearSubscription,\n  };\n}\n"]}
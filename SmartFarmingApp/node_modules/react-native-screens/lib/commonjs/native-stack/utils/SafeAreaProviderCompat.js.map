{"version": 3, "sources": ["SafeAreaProviderCompat.tsx"], "names": ["width", "height", "Dimensions", "get", "initialMetrics", "Platform", "OS", "initialWindowMetrics", "frame", "x", "y", "insets", "top", "left", "right", "bottom", "SafeAreaProviderCompat", "children", "style", "styles", "container", "StyleSheet", "create", "flex"], "mappings": ";;;;;;;AAEA;;AACA;;AAQA;;;;;;AAXA;AACA;AAqBA,MAAM;AAAEA,EAAAA,KAAK,GAAG,CAAV;AAAaC,EAAAA,MAAM,GAAG;AAAtB,IAA4BC,wBAAWC,GAAX,CAAe,QAAf,CAAlC,C,CAEA;AACA;AACA;;;AACA,MAAMC,cAAc,GAClBC,sBAASC,EAAT,KAAgB,KAAhB,IAAyBC,oDAAwB,IAAjD,GACI;AACEC,EAAAA,KAAK,EAAE;AAAEC,IAAAA,CAAC,EAAE,CAAL;AAAQC,IAAAA,CAAC,EAAE,CAAX;AAAcV,IAAAA,KAAd;AAAqBC,IAAAA;AAArB,GADT;AAEEU,EAAAA,MAAM,EAAE;AAAEC,IAAAA,GAAG,EAAE,CAAP;AAAUC,IAAAA,IAAI,EAAE,CAAhB;AAAmBC,IAAAA,KAAK,EAAE,CAA1B;AAA6BC,IAAAA,MAAM,EAAE;AAArC;AAFV,CADJ,GAKIR,gDANN;;AAQe,SAASS,sBAAT,CAAgC;AAAEC,EAAAA,QAAF;AAAYC,EAAAA;AAAZ,CAAhC,EAA4D;AACzE,sBACE,oBAAC,iDAAD,CAAuB,QAAvB,QACIP,MAAD,IAAY;AACX,QAAIA,MAAJ,EAAY;AACV;AACA;AACA;AACA,0BAAO,oBAAC,iBAAD;AAAM,QAAA,KAAK,EAAE,CAACQ,MAAM,CAACC,SAAR,EAAmBF,KAAnB;AAAb,SAAyCD,QAAzC,CAAP;AACD;;AAED,wBACE,oBAAC,4CAAD;AAAkB,MAAA,cAAc,EAAEb,cAAlC;AAAkD,MAAA,KAAK,EAAEc;AAAzD,OACGD,QADH,CADF;AAKD,GAdH,CADF;AAkBD;;AAEDD,sBAAsB,CAACZ,cAAvB,GAAwCA,cAAxC;;AAEA,MAAMe,MAAM,GAAGE,wBAAWC,MAAX,CAAkB;AAC/BF,EAAAA,SAAS,EAAE;AACTG,IAAAA,IAAI,EAAE;AADG;AADoB,CAAlB,CAAf", "sourcesContent": ["// code taken from\n// https://github.com/react-navigation/react-navigation/blob/ec0d113eb25c39ef9defb6c7215640f44e3569ae/packages/elements/src/SafeAreaProviderCompat.tsx\nimport * as React from 'react';\nimport {\n  Dimensions,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\nimport {\n  initialWindowMetrics,\n  SafeAreaInsetsContext,\n  SafeAreaProvider,\n} from 'react-native-safe-area-context';\n\ntype Props = {\n  children: React.ReactNode;\n  style?: StyleProp<ViewStyle>;\n};\n\nconst { width = 0, height = 0 } = Dimensions.get('window');\n\n// To support SSR on web, we need to have empty insets for initial values\n// Otherwise there can be mismatch between SSR and client output\n// We also need to specify empty values to support tests environments\nconst initialMetrics =\n  Platform.OS === 'web' || initialWindowMetrics == null\n    ? {\n        frame: { x: 0, y: 0, width, height },\n        insets: { top: 0, left: 0, right: 0, bottom: 0 },\n      }\n    : initialWindowMetrics;\n\nexport default function SafeAreaProviderCompat({ children, style }: Props) {\n  return (\n    <SafeAreaInsetsContext.Consumer>\n      {(insets) => {\n        if (insets) {\n          // If we already have insets, don't wrap the stack in another safe area provider\n          // This avoids an issue with updates at the cost of potentially incorrect values\n          // https://github.com/react-navigation/react-navigation/issues/174\n          return <View style={[styles.container, style]}>{children}</View>;\n        }\n\n        return (\n          <SafeAreaProvider initialMetrics={initialMetrics} style={style}>\n            {children}\n          </SafeAreaProvider>\n        );\n      }}\n    </SafeAreaInsetsContext.Consumer>\n  );\n}\n\nSafeAreaProviderCompat.initialMetrics = initialMetrics;\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n});\n"]}
{"version": 3, "sources": ["index.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;;AAKA;;AAKA;;AACA", "sourcesContent": ["/**\n * Navigators\n */\nexport { default as createNativeStackNavigator } from './navigators/createNativeStackNavigator';\n\n/**\n * Views\n */\nexport { default as NativeStackView } from './views/NativeStackView';\n\n/**\n * Utilities\n */\nexport { default as useHeaderHeight } from './utils/useHeaderHeight';\nexport { default as HeaderHeightContext } from './utils/HeaderHeightContext';\n\n/**\n * Types\n */\nexport type {\n  NativeStackNavigationOptions,\n  NativeStackNavigationProp,\n  NativeStackScreenProps,\n} from './types';\n"]}
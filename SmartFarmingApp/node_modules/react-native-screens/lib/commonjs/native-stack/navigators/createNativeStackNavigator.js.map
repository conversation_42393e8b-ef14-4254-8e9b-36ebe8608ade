{"version": 3, "sources": ["createNativeStackNavigator.tsx"], "names": ["NativeStackNavigator", "initialRouteName", "children", "screenOptions", "rest", "state", "descriptors", "navigation", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "React", "useEffect", "dangerouslyGetParent", "undefined", "console", "warn", "addListener", "e", "isFocused", "requestAnimationFrame", "index", "defaultPrevented", "dispatch", "StackActions", "popToTop", "target", "key"], "mappings": ";;;;;;;AAAA;;AAWA;;AAMA;;;;;;;;;;AAEA,SAASA,oBAAT,CAA8B;AAC5BC,EAAAA,gBAD4B;AAE5BC,EAAAA,QAF4B;AAG5BC,EAAAA,aAH4B;AAI5B,KAAGC;AAJyB,CAA9B,EAK8B;AAC5B,QAAM;AAAEC,IAAAA,KAAF;AAASC,IAAAA,WAAT;AAAsBC,IAAAA;AAAtB,MAAqC,kCAMzCC,mBANyC,EAM5B;AACbP,IAAAA,gBADa;AAEbC,IAAAA,QAFa;AAGbC,IAAAA;AAHa,GAN4B,CAA3C,CAD4B,CAa5B;AACA;;AACAM,EAAAA,KAAK,CAACC,SAAN,CAAgB,MAAM;AACpB;AACA,QAAI,CAAAH,UAAU,SAAV,IAAAA,UAAU,WAAV,YAAAA,UAAU,CAAEI,oBAAZ,MAAqCC,SAAzC,EAAoD;AAClDC,MAAAA,OAAO,CAACC,IAAR,CACE,2LADF;AAGD;AACF,GAPD,EAOG,CAACP,UAAD,CAPH;AASAE,EAAAA,KAAK,CAACC,SAAN,CACE;AAAA;;AAAA,WACEH,UADF,aACEA,UADF,gDACEA,UAAU,CAAEQ,WADd,0DACE,2BAAAR,UAAU,EAAgB,UAAhB,EAA6BS,CAAD,IAAO;AAC3C,YAAMC,SAAS,GAAGV,UAAU,CAACU,SAAX,EAAlB,CAD2C,CAG3C;AACA;;AACAC,MAAAA,qBAAqB,CAAC,MAAM;AAC1B,YACEb,KAAK,CAACc,KAAN,GAAc,CAAd,IACAF,SADA,IAEA,CAAED,CAAD,CAAkCI,gBAHrC,EAIE;AACA;AACA;AACAb,UAAAA,UAAU,CAACc,QAAX,CAAoB,EAClB,GAAGC,qBAAaC,QAAb,EADe;AAElBC,YAAAA,MAAM,EAAEnB,KAAK,CAACoB;AAFI,WAApB;AAID;AACF,OAboB,CAArB;AAcD,KAnBS,CADZ;AAAA,GADF,EAsBE,CAAClB,UAAD,EAAaF,KAAK,CAACc,KAAnB,EAA0Bd,KAAK,CAACoB,GAAhC,CAtBF;AAyBA,sBACE,oBAAC,wBAAD,eACMrB,IADN;AAEE,IAAA,KAAK,EAAEC,KAFT;AAGE,IAAA,UAAU,EAAEE,UAHd;AAIE,IAAA,WAAW,EAAED;AAJf,KADF;AAQD;;eAEc,oCAKbN,oBALa,C", "sourcesContent": ["import {\n  createNavigator<PERSON><PERSON>y,\n  EventArg,\n  StackActions,\n  StackActionHelpers,\n  StackNavigationState,\n  StackRouter,\n  StackRouterOptions,\n  ParamListBase,\n  useNavigationBuilder,\n} from '@react-navigation/native';\nimport * as React from 'react';\nimport {\n  NativeStackNavigationEventMap,\n  NativeStackNavigationOptions,\n  NativeStackNavigatorProps,\n} from '../types';\nimport NativeStackView from '../views/NativeStackView';\n\nfunction NativeStackNavigator({\n  initialRouteName,\n  children,\n  screenOptions,\n  ...rest\n}: NativeStackNavigatorProps) {\n  const { state, descriptors, navigation } = useNavigationBuilder<\n    StackNavigationState<ParamListBase>,\n    StackRouterOptions,\n    StackActionHelpers<ParamListBase>,\n    NativeStackNavigationOptions,\n    NativeStackNavigationEventMap\n  >(StackRouter, {\n    initialRouteName,\n    children,\n    screenOptions,\n  });\n\n  // Starting from React Navigation v6, `native-stack` should be imported from\n  // `@react-navigation/native-stack` rather than `react-native-screens/native-stack`\n  React.useEffect(() => {\n    // @ts-ignore navigation.dangerouslyGetParent was removed in v6\n    if (navigation?.dangerouslyGetParent === undefined) {\n      console.warn(\n        'Looks like you are importing `native-stack` from `react-native-screens/native-stack`. Since version 6 of `react-navigation`, it should be imported from `@react-navigation/native-stack`.'\n      );\n    }\n  }, [navigation]);\n\n  React.useEffect(\n    () =>\n      navigation?.addListener?.('tabPress', (e) => {\n        const isFocused = navigation.isFocused();\n\n        // Run the operation in the next frame so we're sure all listeners have been run\n        // This is necessary to know if preventDefault() has been called\n        requestAnimationFrame(() => {\n          if (\n            state.index > 0 &&\n            isFocused &&\n            !(e as EventArg<'tabPress', true>).defaultPrevented\n          ) {\n            // When user taps on already focused tab and we're inside the tab,\n            // reset the stack to replicate native behaviour\n            navigation.dispatch({\n              ...StackActions.popToTop(),\n              target: state.key,\n            });\n          }\n        });\n      }),\n    [navigation, state.index, state.key]\n  );\n\n  return (\n    <NativeStackView\n      {...rest}\n      state={state}\n      navigation={navigation}\n      descriptors={descriptors}\n    />\n  );\n}\n\nexport default createNavigatorFactory<\n  StackNavigationState<ParamListBase>,\n  NativeStackNavigationOptions,\n  NativeStackNavigationEventMap,\n  typeof NativeStackNavigator\n>(NativeStackNavigator);\n"]}
{"version": 3, "sources": ["createNativeStackNavigator.tsx"], "names": ["REMOVE_ACTION", "isAndroid", "Platform", "OS", "<PERSON><PERSON><PERSON><PERSON>", "renderComponentOrThunk", "componentOrThunk", "props", "removeScene", "route", "dismissCount", "navigation", "dispatch", "type", "immediate", "key", "onAppear", "descriptor", "options", "StackActions", "completeTransition", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "onFinishTransitioning", "routes", "lastRoute", "length", "renderHeaderConfig", "index", "navigationConfig", "headerMode", "backButtonInCustomView", "direction", "disableBackButtonMenu", "headerBackTitle", "headerBackTitleStyle", "headerBackTitleVisible", "headerHideBackButton", "headerHideShadow", "headerLargeStyle", "headerLargeTitle", "headerLargeTitleHideShadow", "headerLargeTitleStyle", "headerShown", "headerStyle", "headerTintColor", "headerTitleStyle", "headerTopInsetEnabled", "headerTranslucent", "hideShadow", "largeTitle", "largeTitleHideShadow", "title", "translucent", "scene", "headerOptions", "backTitle", "backTitleFontFamily", "fontFamily", "backTitleFontSize", "fontSize", "color", "topInsetEnabled", "hideBackButton", "largeTitleBackgroundColor", "backgroundColor", "largeTitleColor", "largeTitleFontFamily", "largeTitleFontSize", "largeTitleFontWeight", "fontWeight", "titleColor", "titleFontFamily", "titleFontSize", "titleFontWeight", "<PERSON><PERSON><PERSON><PERSON>", "header", "undefined", "blurEffect", "children", "backButtonImage", "push", "searchBar", "headerLeft", "headerBackImage", "goBack", "requestAnimationFrame", "headerPressColorAndroid", "backButtonTitle", "truncatedBackButtonTitle", "backTitleVisible", "layoutPreset", "headerTitle", "headerRight", "MaybeNestedStack", "isHeaderInModal", "screenProps", "SceneComponent", "Screen", "React", "useContext", "ScreenContext", "styles", "scenes", "StyleSheet", "absoluteFill", "StackView", "descriptors", "map", "getComponent", "routeNavigationProp", "mode", "transparentCard", "stackPresentation", "card<PERSON>ran<PERSON>arent", "stackAnimation", "animationEnabled", "console", "warn", "isHeaderInPush", "cardStyle", "customAnimationOnSwipe", "replaceAnimation", "gestureEnabled", "nativeBackButtonDismissalEnabled", "fullScreenSwipeEnabled", "screenOrientation", "statusBarAnimation", "statusBarColor", "statusBarHidden", "statusBarStyle", "statusBarTranslucent", "onWillAppear", "onWillDisappear", "onDisappear", "e", "nativeEvent", "create", "flex", "createStackNavigator", "routeConfigMap", "stackConfig", "router", "superGetStateForAction", "getStateForAction", "action", "backRouteIndex", "backRoute", "find", "indexOf", "newRoutes", "splice", "isTransitioning"], "mappings": ";;;;;;;AAAA;;AACA;;AAQA;;AAaA;;AAkBA;;;;;;AAOA,MAAMA,aAAa,GAAG,6BAAtB;AAEA,MAAMC,SAAS,GAAGC,sBAASC,EAAT,KAAgB,SAAlC;AAEA,IAAIC,OAAO,GAAGH,SAAd;;AAEA,SAASI,sBAAT,CAAgCC,gBAAhC,EAA2DC,KAA3D,EAA2E;AACzE,MAAI,OAAOD,gBAAP,KAA4B,UAAhC,EAA4C;AAC1C,WAAOA,gBAAgB,CAACC,KAAD,CAAvB;AACD;;AACD,SAAOD,gBAAP;AACD;;AAuED,SAASE,WAAT,CACEC,KADF,EAEEC,YAFF,EAGEC,UAHF,EAIE;AACAA,EAAAA,UAAU,CAACC,QAAX,CAAoB;AAClB;AACAC,IAAAA,IAAI,EAAEb,aAFY;AAGlBc,IAAAA,SAAS,EAAE,IAHO;AAIlBC,IAAAA,GAAG,EAAEN,KAAK,CAACM,GAJO;AAKlBL,IAAAA;AALkB,GAApB;AAOD;;AAED,SAASM,QAAT,CACEP,KADF,EAEEQ,UAFF,EAGEN,UAHF,EAIE;AAAA;;AACA,yBAAAM,UAAU,CAACC,OAAX,qGAAoBF,QAApB;AACAL,EAAAA,UAAU,CAACC,QAAX,CACEO,8BAAaC,kBAAb,CAAgC;AAC9BC,IAAAA,UAAU,EAAEZ,KAAK,CAACM,GADY;AAE9BA,IAAAA,GAAG,EAAEJ,UAAU,CAACW,KAAX,CAAiBP;AAFQ,GAAhC,CADF;AAMD;;AAED,SAASQ,qBAAT,CAA+BZ,UAA/B,EAAmE;AACjE,QAAM;AAAEa,IAAAA;AAAF,MAAab,UAAU,CAACW,KAA9B;AACA,QAAMG,SAAS,GAAG,CAAAD,MAAM,SAAN,IAAAA,MAAM,WAAN,YAAAA,MAAM,CAAEE,MAAR,KAAkBF,MAAM,CAACA,MAAM,CAACE,MAAP,GAAgB,CAAjB,CAA1C;;AAEA,MAAID,SAAJ,EAAe;AACbd,IAAAA,UAAU,CAACC,QAAX,CACEO,8BAAaC,kBAAb,CAAgC;AAC9BC,MAAAA,UAAU,EAAEI,SAAS,CAACV,GADQ;AAE9BA,MAAAA,GAAG,EAAEJ,UAAU,CAACW,KAAX,CAAiBP;AAFQ,KAAhC,CADF;AAMD;AACF;;AAED,SAASY,kBAAT,CACEC,KADF,EAEEnB,KAFF,EAGEQ,UAHF,EAIEY,gBAJF,EAKE;AACA,QAAM;AAAEX,IAAAA;AAAF,MAAcD,UAApB;AACA,QAAM;AAAEa,IAAAA;AAAF,MAAiBD,gBAAvB;AAEA,QAAM;AACJE,IAAAA,sBADI;AAEJC,IAAAA,SAFI;AAGJC,IAAAA,qBAHI;AAIJC,IAAAA,eAJI;AAKJC,IAAAA,oBALI;AAMJC,IAAAA,sBANI;AAOJC,IAAAA,oBAPI;AAQJC,IAAAA,gBARI;AASJC,IAAAA,gBATI;AAUJC,IAAAA,gBAVI;AAWJC,IAAAA,0BAXI;AAYJC,IAAAA,qBAZI;AAaJC,IAAAA,WAbI;AAcJC,IAAAA,WAdI;AAeJC,IAAAA,eAfI;AAgBJC,IAAAA,gBAhBI;AAiBJC,IAAAA,qBAAqB,GAAG,IAjBpB;AAkBJC,IAAAA,iBAlBI;AAmBJC,IAAAA,UAnBI;AAoBJC,IAAAA,UApBI;AAqBJC,IAAAA,oBArBI;AAsBJC,IAAAA,KAtBI;AAuBJC,IAAAA;AAvBI,MAwBFnC,OAxBJ;AA0BA,QAAMoC,KAAK,GAAG;AACZ1B,IAAAA,KADY;AAEZb,IAAAA,GAAG,EAAEN,KAAK,CAACM,GAFC;AAGZN,IAAAA,KAHY;AAIZQ,IAAAA;AAJY,GAAd;AAOA,QAAMsC,aAA2C,GAAG;AAClDxB,IAAAA,sBADkD;AAElDyB,IAAAA,SAAS,EAAEpB,sBAAsB,KAAK,KAA3B,GAAmC,EAAnC,GAAwCF,eAFD;AAGlDuB,IAAAA,mBAAmB,EAAEtB,oBAAF,aAAEA,oBAAF,uBAAEA,oBAAoB,CAAEuB,UAHO;AAIlDC,IAAAA,iBAAiB,EAAExB,oBAAF,aAAEA,oBAAF,uBAAEA,oBAAoB,CAAEyB,QAJS;AAKlDC,IAAAA,KAAK,EAAEhB,eAL2C;AAMlDb,IAAAA,SANkD;AAOlDC,IAAAA,qBAPkD;AAQlD6B,IAAAA,eAAe,EAAEf,qBARiC;AASlDgB,IAAAA,cAAc,EAAE1B,oBATkC;AAUlDY,IAAAA,UAAU,EAAEX,gBAAgB,IAAIW,UAVkB;AAWlDC,IAAAA,UAAU,EAAEV,gBAAgB,IAAIU,UAXkB;AAYlDc,IAAAA,yBAAyB,EACvB,CAAAzB,gBAAgB,SAAhB,IAAAA,gBAAgB,WAAhB,YAAAA,gBAAgB,CAAE0B,eAAlB,OACA;AACAvB,IAAAA,qBAFA,aAEAA,qBAFA,uBAEAA,qBAAqB,CAAEuB,eAFvB,CAbgD;AAgBlDC,IAAAA,eAAe,EAAExB,qBAAF,aAAEA,qBAAF,uBAAEA,qBAAqB,CAAEmB,KAhBU;AAiBlDM,IAAAA,oBAAoB,EAAEzB,qBAAF,aAAEA,qBAAF,uBAAEA,qBAAqB,CAAEgB,UAjBK;AAkBlDU,IAAAA,kBAAkB,EAAE1B,qBAAF,aAAEA,qBAAF,uBAAEA,qBAAqB,CAAEkB,QAlBO;AAmBlDS,IAAAA,oBAAoB,EAAE3B,qBAAF,aAAEA,qBAAF,uBAAEA,qBAAqB,CAAE4B,UAnBK;AAoBlDnB,IAAAA,oBAAoB,EAAEA,oBAAoB,IAAIV,0BApBI;AAqBlDW,IAAAA,KArBkD;AAsBlDmB,IAAAA,UAAU,EAAE,CAAAzB,gBAAgB,SAAhB,IAAAA,gBAAgB,WAAhB,YAAAA,gBAAgB,CAAEe,KAAlB,KAA2BhB,eAtBW;AAuBlD2B,IAAAA,eAAe,EAAE1B,gBAAF,aAAEA,gBAAF,uBAAEA,gBAAgB,CAAEY,UAvBe;AAwBlDe,IAAAA,aAAa,EAAE3B,gBAAF,aAAEA,gBAAF,uBAAEA,gBAAgB,CAAEc,QAxBiB;AAyBlDc,IAAAA,eAAe,EAAE5B,gBAAF,aAAEA,gBAAF,uBAAEA,gBAAgB,CAAEwB,UAzBe;AA0BlDjB,IAAAA,WAAW,EAAEL,iBAAiB,IAAIK,WAArB,IAAoC;AA1BC,GAApD;AA6BA,QAAMsB,SAAS,GACbhC,WAAW,KAAK,KAAhB,IAAyBb,UAAU,KAAK,MAAxC,IAAkDZ,OAAO,CAAC0D,MAAR,KAAmB,IADvE;;AAEA,MAAI,CAACD,SAAL,EAAgB;AACd,wBAAO,6BAAC,2CAAD,eAA6BpB,aAA7B;AAA4C,MAAA,MAAM;AAAlD,OAAP;AACD;;AAED,MAAIX,WAAW,KAAKiC,SAApB,EAA+B;AAC7BtB,IAAAA,aAAa,CAACU,eAAd,GAAgCrB,WAAW,CAACqB,eAA5C;AACAV,IAAAA,aAAa,CAACuB,UAAd,GAA2BlC,WAAW,CAACkC,UAAvC;AACD;;AAED,QAAMC,QAAQ,GAAG,EAAjB;;AAEA,MAAI7D,OAAO,CAAC8D,eAAZ,EAA6B;AAC3BD,IAAAA,QAAQ,CAACE,IAAT,eACE,6BAAC,oDAAD;AACE,MAAA,GAAG,EAAC,WADN;AAEE,MAAA,MAAM,EAAE/D,OAAO,CAAC8D;AAFlB,MADF;AAMD;;AAED,MAAI9E,sBAASC,EAAT,KAAgB,KAAhB,IAAyBe,OAAO,CAACgE,SAArC,EAAgD;AAC9CH,IAAAA,QAAQ,CAACE,IAAT,eACE,6BAAC,kDAAD,qBACE,6BAAC,6BAAD,EAAe/D,OAAO,CAACgE,SAAvB,CADF,CADF;AAKD;;AAED,MAAIhE,OAAO,CAACiE,UAAR,KAAuBN,SAA3B,EAAsC;AACpCE,IAAAA,QAAQ,CAACE,IAAT,eACE,6BAAC,6CAAD;AAA2B,MAAA,GAAG,EAAC;AAA/B,OACG5E,sBAAsB,CAACa,OAAO,CAACiE,UAAT,EAAqB;AAAE7B,MAAAA;AAAF,KAArB,CADzB,CADF;AAKD,GAND,MAMO,IAAIpC,OAAO,CAACkE,eAAR,KAA4BP,SAAhC,EAA2C;AAChD,UAAMQ,MAAM,GAAG,MAAM;AACnB;AACAC,MAAAA,qBAAqB,CAAC,MAAM;AAC1BrE,QAAAA,UAAU,CAACN,UAAX,CAAsB0E,MAAtB,CAA6BpE,UAAU,CAACF,GAAxC;AACD,OAFoB,CAArB;AAGD,KALD;;AAOAgE,IAAAA,QAAQ,CAACE,IAAT,eACE,6BAAC,6CAAD;AAA2B,MAAA,GAAG,EAAC;AAA/B,oBACE,6BAAC,sCAAD;AACE,MAAA,OAAO,EAAEI,MADX;AAEE,MAAA,iBAAiB,EAAEnE,OAAO,CAACqE,uBAF7B;AAGE,MAAA,SAAS,EAAErE,OAAO,CAAC2B,eAHrB;AAIE,MAAA,SAAS,EAAE3B,OAAO,CAACkE,eAJrB;AAKE,MAAA,KAAK,EAAElE,OAAO,CAACsE,eALjB;AAME,MAAA,cAAc,EAAEtE,OAAO,CAACuE,wBAN1B;AAOE,MAAA,YAAY,EAAEvE,OAAO,CAACwE,gBAPxB;AAQE,MAAA,UAAU,EAAExE,OAAO,CAACiB,oBARtB;AASE,MAAA,WAAW,EAAEjB,OAAO,CAACyE,YATvB,CAUE;AAVF;AAWE,MAAA,KAAK,EAAEzE,OAAO,CAACsE,eAXjB;AAYE,MAAA,cAAc,EAAEtE,OAAO,CAACuE,wBAZ1B;AAaE,MAAA,gBAAgB,EAAEvE,OAAO,CAACwE,gBAb5B;AAcE,MAAA,UAAU,EAAExE,OAAO,CAACiB,oBAdtB;AAeE,MAAA,YAAY,EAAEjB,OAAO,CAACyE,YAfxB;AAgBE,MAAA,KAAK,EAAErC;AAhBT,MADF,CADF;AAsBD;;AAED,MAAIpC,OAAO,CAAC0E,WAAZ,EAAyB;AACvB,QAAIxC,KAAK,KAAKyB,SAAV,IAAuB,OAAO3D,OAAO,CAAC0E,WAAf,KAA+B,QAA1D,EAAoE;AAClErC,MAAAA,aAAa,CAACH,KAAd,GAAsBlC,OAAO,CAAC0E,WAA9B;AACD,KAFD,MAEO;AACLb,MAAAA,QAAQ,CAACE,IAAT,eACE,6BAAC,+CAAD;AAA6B,QAAA,GAAG,EAAC;AAAjC,SACG5E,sBAAsB,CAACa,OAAO,CAAC0E,WAAT,EAAsB;AAAEtC,QAAAA;AAAF,OAAtB,CADzB,CADF;AAKD;AACF;;AAED,MAAIpC,OAAO,CAAC2E,WAAZ,EAAyB;AACvBd,IAAAA,QAAQ,CAACE,IAAT,eACE,6BAAC,8CAAD;AAA4B,MAAA,GAAG,EAAC;AAAhC,OACG5E,sBAAsB,CAACa,OAAO,CAAC2E,WAAT,EAAsB;AAAEvC,MAAAA;AAAF,KAAtB,CADzB,CADF;AAKD;;AAED,MAAIyB,QAAQ,CAACrD,MAAT,GAAkB,CAAtB,EAAyB;AACvB6B,IAAAA,aAAa,CAACwB,QAAd,GAAyBA,QAAzB;AACD;;AAED,sBAAO,6BAAC,2CAAD,EAA6BxB,aAA7B,CAAP;AACD;;AAED,MAAMuC,gBAAgB,GAAG,CAAC;AACxBC,EAAAA,eADwB;AAExBC,EAAAA,WAFwB;AAGxBvF,EAAAA,KAHwB;AAIxBE,EAAAA,UAJwB;AAKxBsF,EAAAA,cALwB;AAMxBrE,EAAAA,KANwB;AAOxBX,EAAAA,UAPwB;AAQxBY,EAAAA;AARwB,CAAD,KAqBnB;AACJ,QAAMqE,MAAM,GAAGC,eAAMC,UAAN,CAAiBC,iCAAjB,CAAf;;AAEA,MAAIN,eAAJ,EAAqB;AACnB,wBACE,6BAAC,+BAAD;AAAa,MAAA,KAAK,EAAEO,MAAM,CAACC;AAA3B,oBACE,6BAAC,MAAD;AAAQ,MAAA,KAAK,EAAEC,wBAAWC,YAA1B;AAAwC,MAAA,OAAO,MAA/C;AAAgD,MAAA,aAAa;AAA7D,OACG9E,kBAAkB,CAACC,KAAD,EAAQnB,KAAR,EAAeQ,UAAf,EAA2BY,gBAA3B,CADrB,eAEE,6BAAC,0BAAD;AACE,MAAA,WAAW,EAAEmE,WADf;AAEE,MAAA,UAAU,EAAErF,UAFd;AAGE,MAAA,SAAS,EAAEsF;AAHb,MAFF,CADF,CADF;AAYD;;AACD,sBACE,6BAAC,0BAAD;AACE,IAAA,WAAW,EAAED,WADf;AAEE,IAAA,UAAU,EAAErF,UAFd;AAGE,IAAA,SAAS,EAAEsF;AAHb,IADF;AAOD,CA7CD;;AAsDA,SAASS,SAAT,CAAmB;AACjB/F,EAAAA,UADiB;AAEjBgG,EAAAA,WAFiB;AAGjB9E,EAAAA,gBAHiB;AAIjBmE,EAAAA;AAJiB,CAAnB,EAKmB;AACjB,QAAM;AAAExE,IAAAA;AAAF,MAAab,UAAU,CAACW,KAA9B;;AACA,QAAM4E,MAAM,GAAGC,eAAMC,UAAN,CAAiBC,iCAAjB,CAAf;;AACA,sBACE,6BAAC,+BAAD;AACE,IAAA,KAAK,EAAEC,MAAM,CAACC,MADhB;AAEE,IAAA,qBAAqB,EAAE,MAAMhF,qBAAqB,CAACZ,UAAD;AAFpD,KAGGa,MAAM,CAACoF,GAAP,CAAW,CAACnG,KAAD,EAAQmB,KAAR,KAAkB;AAC5B,UAAMX,UAAU,GAAG0F,WAAW,CAAClG,KAAK,CAACM,GAAP,CAA9B;AACA,UAAM;AAAE8F,MAAAA,YAAF;AAAgB3F,MAAAA;AAAhB,QAA4BD,UAAlC;AACA,UAAM6F,mBAAmB,GAAG7F,UAAU,CAACN,UAAvC;AACA,UAAM;AAAEoG,MAAAA,IAAF;AAAQC,MAAAA;AAAR,QAA4BnF,gBAAlC;AACA,UAAMoE,cAAc,GAAGY,YAAY,EAAnC;AAEA,QAAII,iBAAyC,GAAG,MAAhD;;AAEA,QAAI/F,OAAO,CAAC+F,iBAAZ,EAA+B;AAC7BA,MAAAA,iBAAiB,GAAG/F,OAAO,CAAC+F,iBAA5B;AACD,KAFD,MAEO;AACL;AACA,UAAIF,IAAI,KAAK,OAAT,IAAoBA,IAAI,KAAK,gBAAjC,EAAmD;AACjDE,QAAAA,iBAAiB,GAAGF,IAApB;;AACA,YAAIC,eAAe,IAAI9F,OAAO,CAACgG,eAA/B,EAAgD;AAC9CD,UAAAA,iBAAiB,GACfF,IAAI,KAAK,gBAAT,GACI,2BADJ,GAEI,kBAHN;AAID;AACF;AACF;;AACD,QAAII,cAAc,GAAGjG,OAAO,CAACiG,cAA7B;;AACA,QAAIjG,OAAO,CAACkG,gBAAR,KAA6B,KAAjC,EAAwC;AACtCD,MAAAA,cAAc,GAAG,MAAjB;AACD;;AAED,UAAMxC,SAAS,GACbzD,OAAO,CAACyB,WAAR,KAAwB,KAAxB,IACA,CAAAd,gBAAgB,SAAhB,IAAAA,gBAAgB,WAAhB,YAAAA,gBAAgB,CAAEC,UAAlB,MAAiC,MADjC,IAEAZ,OAAO,CAAC0D,MAAR,KAAmB,IAHrB;;AAKA,QACE,CAACxE,OAAD,IACA6G,iBAAiB,KAAK,MADtB,IAEA/F,OAAO,CAACyB,WAAR,KAAwBkC,SAH1B,EAIE;AACAzE,MAAAA,OAAO,GAAG,IAAV;AACAiH,MAAAA,OAAO,CAACC,IAAR,CACE,mHADF;AAGD;;AAED,UAAMvB,eAAe,GAAG9F,SAAS,GAC7B,KAD6B,GAE7BgH,iBAAiB,KAAK,MAAtB,IACAtC,SADA,IAEAzD,OAAO,CAACyB,WAAR,KAAwB,IAJ5B;AAKA,UAAM4E,cAAc,GAAGtH,SAAS,GAC5B0E,SAD4B,GAE5BsC,iBAAiB,KAAK,MAAtB,IAAgCtC,SAFpC;AAIA,wBACE,6BAAC,MAAD;AACE,MAAA,GAAG,EAAG,UAASlE,KAAK,CAACM,GAAI,EAD3B;AAEE,MAAA,OAAO,MAFT;AAGE,MAAA,aAAa,MAHf;AAIE,MAAA,KAAK,EAAE,CAACyF,wBAAWC,YAAZ,EAA0BvF,OAAO,CAACsG,SAAlC,CAJT;AAKE,MAAA,cAAc,EAAEL,cALlB;AAME,MAAA,sBAAsB,EAAEjG,OAAO,CAACuG,sBANlC;AAOE,MAAA,iBAAiB,EAAER,iBAPrB;AAQE,MAAA,gBAAgB,EACd/F,OAAO,CAACwG,gBAAR,KAA6B7C,SAA7B,GACI,KADJ,GAEI3D,OAAO,CAACwG,gBAXhB;AAaE,MAAA,aAAa,EACX9F,KAAK,KAAKjB,UAAU,CAACW,KAAX,CAAiBE,MAAjB,CAAwBE,MAAxB,GAAiC,CAA3C,GAA+C,MAA/C,GAAwD,MAd5D;AAgBE,MAAA,cAAc,EACZxB,sBAASC,EAAT,KAAgB,SAAhB,GACI,KADJ,GAEIe,OAAO,CAACyG,cAAR,KAA2B9C,SAA3B,GACA,IADA,GAEA3D,OAAO,CAACyG,cArBhB;AAuBE,MAAA,gCAAgC,EAC9BzG,OAAO,CAAC0G,gCAxBZ;AA0BE,MAAA,sBAAsB,EAAE1G,OAAO,CAAC2G,sBA1BlC;AA2BE,MAAA,iBAAiB,EAAE3G,OAAO,CAAC4G,iBA3B7B;AA4BE,MAAA,kBAAkB,EAAE5G,OAAO,CAAC6G,kBA5B9B;AA6BE,MAAA,cAAc,EAAE7G,OAAO,CAAC8G,cA7B1B;AA8BE,MAAA,eAAe,EAAE9G,OAAO,CAAC+G,eA9B3B;AA+BE,MAAA,cAAc,EAAE/G,OAAO,CAACgH,cA/B1B;AAgCE,MAAA,oBAAoB,EAAEhH,OAAO,CAACiH,oBAhChC;AAiCE,MAAA,QAAQ,EAAE,MAAMnH,QAAQ,CAACP,KAAD,EAAQQ,UAAR,EAAoB6F,mBAApB,CAjC1B;AAkCE,MAAA,YAAY,EAAE;AAAA;;AAAA,eAAM5F,OAAN,aAAMA,OAAN,gDAAMA,OAAO,CAAEkH,YAAf,0DAAM,2BAAAlH,OAAO,CAAb;AAAA,OAlChB;AAmCE,MAAA,eAAe,EAAE;AAAA;;AAAA,eAAMA,OAAN,aAAMA,OAAN,gDAAMA,OAAO,CAAEmH,eAAf,0DAAM,2BAAAnH,OAAO,CAAb;AAAA,OAnCnB;AAoCE,MAAA,WAAW,EAAE;AAAA;;AAAA,eAAMA,OAAN,aAAMA,OAAN,+CAAMA,OAAO,CAAEoH,WAAf,yDAAM,0BAAApH,OAAO,CAAb;AAAA,OApCf;AAqCE,MAAA,yBAAyB,EAAE,MACzBV,WAAW,CAACC,KAAD,EAAQ,CAAR,EAAWqG,mBAAX,CAtCf;AAwCE,MAAA,WAAW,EAAGyB,CAAD,IACX/H,WAAW,CACTC,KADS,EAET8H,CAAC,CAACC,WAAF,CAAc9H,YAFL,EAGToG,mBAHS;AAzCf,OA+CGS,cAAc,IACb5F,kBAAkB,CAACC,KAAD,EAAQnB,KAAR,EAAeQ,UAAf,EAA2BY,gBAA3B,CAhDtB,eAiDE,6BAAC,gBAAD;AACE,MAAA,eAAe,EAAEkE,eADnB;AAEE,MAAA,WAAW,EAAEC,WAFf;AAGE,MAAA,KAAK,EAAEvF,KAHT;AAIE,MAAA,UAAU,EAAEqG,mBAJd;AAKE,MAAA,cAAc,EAAEb,cALlB;AAME,MAAA,KAAK,EAAErE,KANT;AAOE,MAAA,UAAU,EAAEX,UAPd;AAQE,MAAA,gBAAgB,EAAEY;AARpB,MAjDF,CADF;AA8DD,GAnHA,CAHH,CADF;AA0HD;;AAED,MAAMyE,MAAM,GAAGE,wBAAWiC,MAAX,CAAkB;AAC/BlC,EAAAA,MAAM,EAAE;AAAEmC,IAAAA,IAAI,EAAE;AAAR;AADuB,CAAlB,CAAf;;AAIA,SAASC,oBAAT,CACEC,cADF,EAKEC,WAKC,GAAG,EAVN,EAcE;AACA,QAAMC,MAAM,GAAG,kCAAYF,cAAZ,EAA4BC,WAA5B,CAAf,CADA,CAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,QAAME,sBAAsB,GAAGD,MAAM,CAACE,iBAAtC;;AACAF,EAAAA,MAAM,CAACE,iBAAP,GAA2B,CACzBC,MADyB,EAEzB3H,KAFyB,KAGtB;AACH,QAAI2H,MAAM,CAACpI,IAAP,KAAgBb,aAApB,EAAmC;AACjC,YAAM;AAAEe,QAAAA,GAAF;AAAOD,QAAAA,SAAP;AAAkBJ,QAAAA;AAAlB,UAAmCuI,MAAzC;AACA,UAAIC,cAAc,GAAG5H,KAAK,CAACM,KAA3B;;AACA,UAAIb,GAAJ,EAAS;AACP,cAAMoI,SAAS,GAAG7H,KAAK,CAACE,MAAN,CAAa4H,IAAb,CACf3I,KAAD,IAA8CA,KAAK,CAACM,GAAN,KAAcA,GAD5C,CAAlB;AAGAmI,QAAAA,cAAc,GAAG5H,KAAK,CAACE,MAAN,CAAa6H,OAAb,CAAqBF,SAArB,CAAjB;AACD;;AAED,UAAID,cAAc,GAAG,CAArB,EAAwB;AACtB,cAAMI,SAAS,GAAG,CAAC,GAAGhI,KAAK,CAACE,MAAV,CAAlB;;AACA,YAAId,YAAY,GAAG,CAAnB,EAAsB;AACpB;AACA;AACA;AACA4I,UAAAA,SAAS,CAACC,MAAV,CAAiBL,cAAc,GAAGxI,YAAjB,GAAgC,CAAjD,EAAoDA,YAApD;AACD,SALD,MAKO;AACL4I,UAAAA,SAAS,CAACC,MAAV,CAAiBL,cAAjB,EAAiC,CAAjC;AACD;;AAED,eAAO,EACL,GAAG5H,KADE;AAELE,UAAAA,MAAM,EAAE8H,SAFH;AAGL1H,UAAAA,KAAK,EAAE0H,SAAS,CAAC5H,MAAV,GAAmB,CAHrB;AAIL8H,UAAAA,eAAe,EAAE1I,SAAS,KAAK;AAJ1B,SAAP;AAMD;AACF;;AACD,WAAOiI,sBAAsB,CAACE,MAAD,EAA6B3H,KAA7B,CAA7B;AACD,GAlCD,CAXA,CA8CA;;;AACA,SAAO,sCAAgBoF,SAAhB,EAA2BoC,MAA3B,EAAmCD,WAAnC,CAAP;AACD;;eAEcF,oB", "sourcesContent": ["import React from 'react';\nimport {\n  Platform,\n  StyleSheet,\n  Animated,\n  StyleProp,\n  TextStyle,\n  ViewStyle,\n} from 'react-native';\nimport {\n  ScreenContext,\n  ScreenStack,\n  ScreenStackHeaderBackButtonImage,\n  ScreenStackHeaderCenterView,\n  ScreenStackHeaderConfig,\n  ScreenStackHeaderConfigProps,\n  ScreenStackHeaderLeftView,\n  ScreenStackHeaderRightView,\n  ScreenStackHeaderSearchBarView,\n  SearchBar,\n  StackPresentationTypes,\n} from 'react-native-screens';\nimport {\n  createNavigator,\n  SceneView,\n  StackActions,\n  StackRouter,\n  NavigationRouteConfigMap,\n  CreateNavigatorConfig,\n  NavigationStackRouterConfig,\n  NavigationParams,\n  NavigationRoute,\n  NavigationDescriptor,\n  NavigationState,\n  NavigationNavigator,\n  NavigationAction,\n  NavigationProp,\n  NavigationScreenProp,\n} from 'react-navigation';\nimport { NativeStackNavigationOptions as NativeStackNavigationOptionsV5 } from './native-stack/types';\nimport { HeaderBackButton } from 'react-navigation-stack';\nimport {\n  StackNavigationHelpers,\n  StackNavigationProp,\n  Layout,\n} from 'react-navigation-stack/src/vendor/types';\n\nconst REMOVE_ACTION = 'NativeStackNavigator/REMOVE';\n\nconst isAndroid = Platform.OS === 'android';\n\nlet didWarn = isAndroid;\n\nfunction renderComponentOrThunk(componentOrThunk: unknown, props: unknown) {\n  if (typeof componentOrThunk === 'function') {\n    return componentOrThunk(props);\n  }\n  return componentOrThunk;\n}\n\ntype NativeStackRemoveNavigationAction = {\n  type: typeof REMOVE_ACTION;\n  immediate: boolean;\n  dismissCount: number;\n  key?: string;\n};\n\nexport type NativeStackNavigationProp = StackNavigationProp;\n\nexport type NativeStackNavigationOptions = StackNavigatorOptions &\n  NativeStackNavigationOptionsV5 &\n  BackButtonProps & {\n    onWillAppear?: () => void;\n    onAppear?: () => void;\n    onWillDisappear?: () => void;\n    onDisappear?: () => void;\n    // these props differ from the ones used in v5 `native-stack`, and we would like to keep the API consistent between versions\n    /** Use `headerHideShadow` to be consistent with v5 `native-stack` */\n    hideShadow?: boolean;\n    /** Use `headerLargeTitle` to be consistent with v5 `native-stack` */\n    largeTitle?: boolean;\n    /** Use `headerLargeTitleHideShadow` to be consistent with v5 `native-stack` */\n    largeTitleHideShadow?: boolean;\n    /** Use `headerTranslucent` to be consistent with v5 `native-stack` */\n    translucent?: boolean;\n  };\n\n// these are adopted from `stack` navigator\ntype StackNavigatorOptions = {\n  /** This is an option from `stackNavigator` and it hides the header when set to `null`. Use `headerShown` instead to be consistent with v5 `native-stack`. */\n  header?: React.ComponentType<Record<string, unknown>> | null;\n  /** This is an option from `stackNavigator` and it controls the stack presentation along with `mode` prop. Use `stackPresentation` instead to be consistent with v5 `native-stack` */\n  cardTransparent?: boolean;\n  /** This is an option from `stackNavigator` and it sets stack animation to none when `false` passed. Use `stackAnimation: 'none'` instead to be consistent with v5 `native-stack` */\n  animationEnabled?: boolean;\n  cardStyle?: StyleProp<ViewStyle>;\n};\n\n// these are the props used for rendering back button taken from `react-navigation-stack`\ntype BackButtonProps = {\n  headerBackImage?: (props: { tintColor: string }) => React.ReactNode;\n  headerPressColorAndroid?: string;\n  headerTintColor?: string;\n  backButtonTitle?: string;\n  truncatedBackButtonTitle?: string;\n  backTitleVisible?: boolean;\n  headerBackTitleStyle?: Animated.WithAnimatedValue<StyleProp<TextStyle>>;\n  layoutPreset?: Layout;\n};\n\ntype NativeStackDescriptor = NavigationDescriptor<\n  NavigationParams,\n  NativeStackNavigationOptions\n>;\n\ntype NativeStackDescriptorMap = {\n  [key: string]: NativeStackDescriptor;\n};\n\n// these are the props used for rendering back button taken from `react-navigation-stack`\ntype NativeStackNavigationConfig = {\n  /** This is an option from `stackNavigator` and controls the stack presentation along with `cardTransparent` prop. Use `stackPresentation` instead to be consistent with v5 `native-stack` */\n  mode?: 'modal' | 'containedModal';\n  /** This is an option from `stackNavigator` and makes the header hide when set to `none`. Use `headerShown` instead to be consistent with v5 `native-stack` */\n  headerMode?: 'none';\n  /** This is an option from `stackNavigator` and controls the stack presentation along with `mode` prop. Use `stackPresentation` instead to be consistent with v5 `native-stack` */\n  transparentCard?: boolean;\n};\n\nfunction removeScene(\n  route: NavigationRoute<NavigationParams>,\n  dismissCount: number,\n  navigation: StackNavigationHelpers\n) {\n  navigation.dispatch({\n    // @ts-ignore special navigation action for native stack\n    type: REMOVE_ACTION,\n    immediate: true,\n    key: route.key,\n    dismissCount,\n  });\n}\n\nfunction onAppear(\n  route: NavigationRoute<NavigationParams>,\n  descriptor: NativeStackDescriptor,\n  navigation: StackNavigationHelpers\n) {\n  descriptor.options?.onAppear?.();\n  navigation.dispatch(\n    StackActions.completeTransition({\n      toChildKey: route.key,\n      key: navigation.state.key,\n    })\n  );\n}\n\nfunction onFinishTransitioning(navigation: StackNavigationHelpers) {\n  const { routes } = navigation.state;\n  const lastRoute = routes?.length && routes[routes.length - 1];\n\n  if (lastRoute) {\n    navigation.dispatch(\n      StackActions.completeTransition({\n        toChildKey: lastRoute.key,\n        key: navigation.state.key,\n      })\n    );\n  }\n}\n\nfunction renderHeaderConfig(\n  index: number,\n  route: NavigationRoute<NavigationParams>,\n  descriptor: NativeStackDescriptor,\n  navigationConfig: NativeStackNavigationConfig\n) {\n  const { options } = descriptor;\n  const { headerMode } = navigationConfig;\n\n  const {\n    backButtonInCustomView,\n    direction,\n    disableBackButtonMenu,\n    headerBackTitle,\n    headerBackTitleStyle,\n    headerBackTitleVisible,\n    headerHideBackButton,\n    headerHideShadow,\n    headerLargeStyle,\n    headerLargeTitle,\n    headerLargeTitleHideShadow,\n    headerLargeTitleStyle,\n    headerShown,\n    headerStyle,\n    headerTintColor,\n    headerTitleStyle,\n    headerTopInsetEnabled = true,\n    headerTranslucent,\n    hideShadow,\n    largeTitle,\n    largeTitleHideShadow,\n    title,\n    translucent,\n  } = options;\n\n  const scene = {\n    index,\n    key: route.key,\n    route,\n    descriptor,\n  };\n\n  const headerOptions: ScreenStackHeaderConfigProps = {\n    backButtonInCustomView,\n    backTitle: headerBackTitleVisible === false ? '' : headerBackTitle,\n    backTitleFontFamily: headerBackTitleStyle?.fontFamily,\n    backTitleFontSize: headerBackTitleStyle?.fontSize,\n    color: headerTintColor,\n    direction,\n    disableBackButtonMenu,\n    topInsetEnabled: headerTopInsetEnabled,\n    hideBackButton: headerHideBackButton,\n    hideShadow: headerHideShadow || hideShadow,\n    largeTitle: headerLargeTitle || largeTitle,\n    largeTitleBackgroundColor:\n      headerLargeStyle?.backgroundColor ||\n      // @ts-ignore old implementation, will not be present in TS API, but can be used here\n      headerLargeTitleStyle?.backgroundColor,\n    largeTitleColor: headerLargeTitleStyle?.color,\n    largeTitleFontFamily: headerLargeTitleStyle?.fontFamily,\n    largeTitleFontSize: headerLargeTitleStyle?.fontSize,\n    largeTitleFontWeight: headerLargeTitleStyle?.fontWeight,\n    largeTitleHideShadow: largeTitleHideShadow || headerLargeTitleHideShadow,\n    title,\n    titleColor: headerTitleStyle?.color || headerTintColor,\n    titleFontFamily: headerTitleStyle?.fontFamily,\n    titleFontSize: headerTitleStyle?.fontSize,\n    titleFontWeight: headerTitleStyle?.fontWeight,\n    translucent: headerTranslucent || translucent || false,\n  };\n\n  const hasHeader =\n    headerShown !== false && headerMode !== 'none' && options.header !== null;\n  if (!hasHeader) {\n    return <ScreenStackHeaderConfig {...headerOptions} hidden />;\n  }\n\n  if (headerStyle !== undefined) {\n    headerOptions.backgroundColor = headerStyle.backgroundColor;\n    headerOptions.blurEffect = headerStyle.blurEffect;\n  }\n\n  const children = [];\n\n  if (options.backButtonImage) {\n    children.push(\n      <ScreenStackHeaderBackButtonImage\n        key=\"backImage\"\n        source={options.backButtonImage}\n      />\n    );\n  }\n\n  if (Platform.OS === 'ios' && options.searchBar) {\n    children.push(\n      <ScreenStackHeaderSearchBarView>\n        <SearchBar {...options.searchBar} />\n      </ScreenStackHeaderSearchBarView>\n    );\n  }\n\n  if (options.headerLeft !== undefined) {\n    children.push(\n      <ScreenStackHeaderLeftView key=\"left\">\n        {renderComponentOrThunk(options.headerLeft, { scene })}\n      </ScreenStackHeaderLeftView>\n    );\n  } else if (options.headerBackImage !== undefined) {\n    const goBack = () => {\n      // Go back on next tick because button ripple effect needs to happen on Android\n      requestAnimationFrame(() => {\n        descriptor.navigation.goBack(descriptor.key);\n      });\n    };\n\n    children.push(\n      <ScreenStackHeaderLeftView key=\"left\">\n        <HeaderBackButton\n          onPress={goBack}\n          pressColorAndroid={options.headerPressColorAndroid}\n          tintColor={options.headerTintColor}\n          backImage={options.headerBackImage}\n          label={options.backButtonTitle}\n          truncatedLabel={options.truncatedBackButtonTitle}\n          labelVisible={options.backTitleVisible}\n          labelStyle={options.headerBackTitleStyle}\n          titleLayout={options.layoutPreset}\n          // @ts-ignore old props kept for very old version of `react-navigation-stack`\n          title={options.backButtonTitle}\n          truncatedTitle={options.truncatedBackButtonTitle}\n          backTitleVisible={options.backTitleVisible}\n          titleStyle={options.headerBackTitleStyle}\n          layoutPreset={options.layoutPreset}\n          scene={scene}\n        />\n      </ScreenStackHeaderLeftView>\n    );\n  }\n\n  if (options.headerTitle) {\n    if (title === undefined && typeof options.headerTitle === 'string') {\n      headerOptions.title = options.headerTitle;\n    } else {\n      children.push(\n        <ScreenStackHeaderCenterView key=\"center\">\n          {renderComponentOrThunk(options.headerTitle, { scene })}\n        </ScreenStackHeaderCenterView>\n      );\n    }\n  }\n\n  if (options.headerRight) {\n    children.push(\n      <ScreenStackHeaderRightView key=\"right\">\n        {renderComponentOrThunk(options.headerRight, { scene })}\n      </ScreenStackHeaderRightView>\n    );\n  }\n\n  if (children.length > 0) {\n    headerOptions.children = children;\n  }\n\n  return <ScreenStackHeaderConfig {...headerOptions} />;\n}\n\nconst MaybeNestedStack = ({\n  isHeaderInModal,\n  screenProps,\n  route,\n  navigation,\n  SceneComponent,\n  index,\n  descriptor,\n  navigationConfig,\n}: {\n  isHeaderInModal: boolean;\n  screenProps: unknown;\n  route: NavigationRoute<NavigationParams>;\n  navigation: NavigationScreenProp<\n    NavigationRoute<NavigationParams>,\n    NavigationParams\n  >;\n  SceneComponent: React.ComponentType<Record<string, unknown>>;\n  index: number;\n  descriptor: NativeStackDescriptor;\n  navigationConfig: NativeStackNavigationConfig;\n}) => {\n  const Screen = React.useContext(ScreenContext);\n\n  if (isHeaderInModal) {\n    return (\n      <ScreenStack style={styles.scenes}>\n        <Screen style={StyleSheet.absoluteFill} enabled isNativeStack>\n          {renderHeaderConfig(index, route, descriptor, navigationConfig)}\n          <SceneView\n            screenProps={screenProps}\n            navigation={navigation}\n            component={SceneComponent}\n          />\n        </Screen>\n      </ScreenStack>\n    );\n  }\n  return (\n    <SceneView\n      screenProps={screenProps}\n      navigation={navigation}\n      component={SceneComponent}\n    />\n  );\n};\n\ntype StackViewProps = {\n  navigation: StackNavigationHelpers;\n  descriptors: NativeStackDescriptorMap;\n  navigationConfig: NativeStackNavigationConfig;\n  screenProps: unknown;\n};\n\nfunction StackView({\n  navigation,\n  descriptors,\n  navigationConfig,\n  screenProps,\n}: StackViewProps) {\n  const { routes } = navigation.state;\n  const Screen = React.useContext(ScreenContext);\n  return (\n    <ScreenStack\n      style={styles.scenes}\n      onFinishTransitioning={() => onFinishTransitioning(navigation)}>\n      {routes.map((route, index) => {\n        const descriptor = descriptors[route.key];\n        const { getComponent, options } = descriptor;\n        const routeNavigationProp = descriptor.navigation;\n        const { mode, transparentCard } = navigationConfig;\n        const SceneComponent = getComponent();\n\n        let stackPresentation: StackPresentationTypes = 'push';\n\n        if (options.stackPresentation) {\n          stackPresentation = options.stackPresentation;\n        } else {\n          // this shouldn't be used because we have a prop for that\n          if (mode === 'modal' || mode === 'containedModal') {\n            stackPresentation = mode;\n            if (transparentCard || options.cardTransparent) {\n              stackPresentation =\n                mode === 'containedModal'\n                  ? 'containedTransparentModal'\n                  : 'transparentModal';\n            }\n          }\n        }\n        let stackAnimation = options.stackAnimation;\n        if (options.animationEnabled === false) {\n          stackAnimation = 'none';\n        }\n\n        const hasHeader =\n          options.headerShown !== false &&\n          navigationConfig?.headerMode !== 'none' &&\n          options.header !== null;\n\n        if (\n          !didWarn &&\n          stackPresentation !== 'push' &&\n          options.headerShown !== undefined\n        ) {\n          didWarn = true;\n          console.warn(\n            'Be aware that changing the visibility of header in modal on iOS will result in resetting the state of the screen.'\n          );\n        }\n\n        const isHeaderInModal = isAndroid\n          ? false\n          : stackPresentation !== 'push' &&\n            hasHeader &&\n            options.headerShown === true;\n        const isHeaderInPush = isAndroid\n          ? hasHeader\n          : stackPresentation === 'push' && hasHeader;\n\n        return (\n          <Screen\n            key={`screen_${route.key}`}\n            enabled\n            isNativeStack\n            style={[StyleSheet.absoluteFill, options.cardStyle]}\n            stackAnimation={stackAnimation}\n            customAnimationOnSwipe={options.customAnimationOnSwipe}\n            stackPresentation={stackPresentation}\n            replaceAnimation={\n              options.replaceAnimation === undefined\n                ? 'pop'\n                : options.replaceAnimation\n            }\n            pointerEvents={\n              index === navigation.state.routes.length - 1 ? 'auto' : 'none'\n            }\n            gestureEnabled={\n              Platform.OS === 'android'\n                ? false\n                : options.gestureEnabled === undefined\n                ? true\n                : options.gestureEnabled\n            }\n            nativeBackButtonDismissalEnabled={\n              options.nativeBackButtonDismissalEnabled\n            }\n            fullScreenSwipeEnabled={options.fullScreenSwipeEnabled}\n            screenOrientation={options.screenOrientation}\n            statusBarAnimation={options.statusBarAnimation}\n            statusBarColor={options.statusBarColor}\n            statusBarHidden={options.statusBarHidden}\n            statusBarStyle={options.statusBarStyle}\n            statusBarTranslucent={options.statusBarTranslucent}\n            onAppear={() => onAppear(route, descriptor, routeNavigationProp)}\n            onWillAppear={() => options?.onWillAppear?.()}\n            onWillDisappear={() => options?.onWillDisappear?.()}\n            onDisappear={() => options?.onDisappear?.()}\n            onHeaderBackButtonClicked={() =>\n              removeScene(route, 1, routeNavigationProp)\n            }\n            onDismissed={(e) =>\n              removeScene(\n                route,\n                e.nativeEvent.dismissCount,\n                routeNavigationProp\n              )\n            }>\n            {isHeaderInPush &&\n              renderHeaderConfig(index, route, descriptor, navigationConfig)}\n            <MaybeNestedStack\n              isHeaderInModal={isHeaderInModal}\n              screenProps={screenProps}\n              route={route}\n              navigation={routeNavigationProp}\n              SceneComponent={SceneComponent}\n              index={index}\n              descriptor={descriptor}\n              navigationConfig={navigationConfig}\n            />\n          </Screen>\n        );\n      })}\n    </ScreenStack>\n  );\n}\n\nconst styles = StyleSheet.create({\n  scenes: { flex: 1 },\n});\n\nfunction createStackNavigator(\n  routeConfigMap: NavigationRouteConfigMap<\n    NativeStackNavigationOptions,\n    StackNavigationProp\n  >,\n  stackConfig: CreateNavigatorConfig<\n    NativeStackNavigationConfig,\n    NavigationStackRouterConfig,\n    NativeStackNavigationOptions,\n    StackNavigationProp\n  > = {}\n): NavigationNavigator<\n  Record<string, unknown>,\n  NavigationProp<NavigationState>\n> {\n  const router = StackRouter(routeConfigMap, stackConfig);\n\n  // below we override getStateForAction method in order to add handling for\n  // a custom native stack navigation action. The action REMOVE that we want to\n  // add works in a similar way to POP, but it does not remove all the routes\n  // that sit on top of the removed route. For example if we have three routes\n  // [a,b,c] and call POP on b, then both b and c will go away. In case we\n  // call REMOVE on b, only b will be removed from the stack and the resulting\n  // state will be [a, c]\n  const superGetStateForAction = router.getStateForAction;\n  router.getStateForAction = (\n    action: NavigationAction | NativeStackRemoveNavigationAction,\n    state\n  ) => {\n    if (action.type === REMOVE_ACTION) {\n      const { key, immediate, dismissCount } = action;\n      let backRouteIndex = state.index;\n      if (key) {\n        const backRoute = state.routes.find(\n          (route: NavigationRoute<NavigationParams>) => route.key === key\n        );\n        backRouteIndex = state.routes.indexOf(backRoute);\n      }\n\n      if (backRouteIndex > 0) {\n        const newRoutes = [...state.routes];\n        if (dismissCount > 1) {\n          // when dismissing with iOS 14 native header back button, we can pop more than 1 screen at a time\n          // and the `backRouteIndex` is the index of the previous screen. Since we are starting already\n          // on the previous screen, we add 1 to start.\n          newRoutes.splice(backRouteIndex - dismissCount + 1, dismissCount);\n        } else {\n          newRoutes.splice(backRouteIndex, 1);\n        }\n\n        return {\n          ...state,\n          routes: newRoutes,\n          index: newRoutes.length - 1,\n          isTransitioning: immediate !== true,\n        };\n      }\n    }\n    return superGetStateForAction(action as NavigationAction, state);\n  };\n  // Create a navigator with StackView as the view\n  return createNavigator(StackView, router, stackConfig);\n}\n\nexport default createStackNavigator;\n"]}
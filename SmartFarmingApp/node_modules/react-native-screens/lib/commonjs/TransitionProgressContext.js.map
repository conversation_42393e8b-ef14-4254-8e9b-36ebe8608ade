{"version": 3, "sources": ["TransitionProgressContext.tsx"], "names": ["React", "createContext", "undefined"], "mappings": ";;;;;;;AAAA;;;;;;4BASeA,KAAK,CAACC,aAAN,CACbC,SADa,C", "sourcesContent": ["import * as React from 'react';\nimport { Animated } from 'react-native';\n\ntype TransitionProgressContextBody = {\n  progress: Animated.Value;\n  closing: Animated.Value;\n  goingForward: Animated.Value;\n};\n\nexport default React.createContext<TransitionProgressContextBody | undefined>(\n  undefined\n);\n"]}
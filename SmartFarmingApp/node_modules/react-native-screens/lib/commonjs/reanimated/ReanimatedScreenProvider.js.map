{"version": 3, "sources": ["ReanimatedScreenProvider.tsx"], "names": ["ReanimatedScreenWrapper", "React", "Component", "ref", "props", "onComponentRef", "setNativeProps", "render", "ReanimatedScreen", "isNativeStack", "ReanimatedNativeStackScreen", "AnimatedScreen", "setRef", "ReanimatedScreenProvider", "children"], "mappings": ";;;;;;;AAAA;;AAEA;;AACA;;AACA;;;;;;;;AAEA,MAAMA,uBAAN,SAAsCC,eAAMC,SAA5C,CAAmE;AAAA;AAAA;;AAAA,iCACb,IADa;;AAAA,oCAOvDC,GAAD,IAAqD;AAAA;;AAC5D,WAAKA,GAAL,GAAWA,GAAX;AACA,mDAAKC,KAAL,EAAWC,cAAX,kGAA4BF,GAA5B;AACD,KAVgE;AAAA;;AAGjEG,EAAAA,cAAc,CAACF,KAAD,EAA2B;AAAA;;AACvC,sBAAKD,GAAL,wDAAUG,cAAV,CAAyBF,KAAzB;AACD;;AAODG,EAAAA,MAAM,GAAG;AACP,UAAMC,gBAAgB,GAAG,KAAKJ,KAAL,CAAWK,aAAX,GACrBC,oCADqB,GAErBC,yBAFJ;AAGA,wBACE,6BAAC,gBAAD,eACM,KAAKP,KADX;AAEE;AACA,MAAA,GAAG,EAAE,KAAKQ;AAHZ,OADF;AAOD;;AAvBgE;;AA0BpD,SAASC,wBAAT,CACbT,KADa,EAEb;AACA;AAAA;AACE;AACA,iCAAC,iCAAD,CAAe,QAAf;AAAwB,MAAA,KAAK,EAAEJ;AAA/B,OACGI,KAAK,CAACU,QADT;AAFF;AAMD", "sourcesContent": ["import React, { PropsWithChildren } from 'react';\nimport { View } from 'react-native';\nimport { ScreenProps, ScreenContext } from 'react-native-screens';\nimport ReanimatedNativeStackScreen from './ReanimatedNativeStackScreen';\nimport AnimatedScreen from './ReanimatedScreen';\n\nclass ReanimatedScreenWrapper extends React.Component<ScreenProps> {\n  private ref: React.ElementRef<typeof View> | null = null;\n\n  setNativeProps(props: ScreenProps): void {\n    this.ref?.setNativeProps(props);\n  }\n\n  setRef = (ref: React.ElementRef<typeof View> | null): void => {\n    this.ref = ref;\n    this.props.onComponentRef?.(ref);\n  };\n\n  render() {\n    const ReanimatedScreen = this.props.isNativeStack\n      ? ReanimatedNativeStackScreen\n      : AnimatedScreen;\n    return (\n      <ReanimatedScreen\n        {...this.props}\n        // @ts-ignore some problems with ref\n        ref={this.setRef}\n      />\n    );\n  }\n}\n\nexport default function ReanimatedScreenProvider(\n  props: PropsWithChildren<unknown>\n) {\n  return (\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    <ScreenContext.Provider value={ReanimatedScreenWrapper as any}>\n      {props.children}\n    </ScreenContext.Provider>\n  );\n}\n"]}
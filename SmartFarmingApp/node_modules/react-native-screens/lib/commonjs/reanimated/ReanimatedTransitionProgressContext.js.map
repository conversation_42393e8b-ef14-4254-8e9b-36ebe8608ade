{"version": 3, "sources": ["ReanimatedTransitionProgressContext.tsx"], "names": ["React", "createContext", "undefined"], "mappings": ";;;;;;;AAAA;;;;;;4BAUeA,KAAK,CAACC,aAAN,CAEbC,SAFa,C", "sourcesContent": ["import * as React from 'react';\n// @ts-ignore file to be used only if `react-native-reanimated` available in the project\nimport Animated from 'react-native-reanimated';\n\ntype ReanimatedTransitionProgressContextBody = {\n  progress: Animated.SharedValue<number>;\n  closing: Animated.SharedValue<number>;\n  goingForward: Animated.SharedValue<number>;\n};\n\nexport default React.createContext<\n  ReanimatedTransitionProgressContextBody | undefined\n>(undefined);\n"]}
{"version": 3, "sources": ["ReanimatedScreen.tsx"], "names": ["AnimatedScreen", "Animated", "createAnimatedComponent", "InnerScreen", "ReanimatedScreen", "React", "forwardRef", "props", "ref", "displayName"], "mappings": ";;;;;;;AAAA;;AACA;;AAGA;;;;;;AAEA,MAAMA,cAAc,GAAGC,+BAASC,uBAAT,CACpBC,+BADoB,CAAvB;;AAIA,MAAMC,gBAAgB,gBAAGC,eAAMC,UAAN,CACvB,CAACC,KAAD,EAAQC,GAAR,KAAgB;AACd,sBACE,6BAAC,cAAD,CACE;AADF;AAEE,IAAA,GAAG,EAAEA;AAFP,KAGMD,KAHN,EADF;AAOD,CATsB,CAAzB;;AAYAH,gBAAgB,CAACK,WAAjB,GAA+B,kBAA/B;eAEeL,gB", "sourcesContent": ["import React from 'react';\nimport { InnerScreen, ScreenProps } from 'react-native-screens';\n\n// @ts-ignore file to be used only if `react-native-reanimated` available in the project\nimport Animated from 'react-native-reanimated';\n\nconst AnimatedScreen = Animated.createAnimatedComponent(\n  (InnerScreen as unknown) as React.ComponentClass\n);\n\nconst ReanimatedScreen = React.forwardRef<typeof AnimatedScreen, ScreenProps>(\n  (props, ref) => {\n    return (\n      <AnimatedScreen\n        // @ts-ignore some problems with ref and onTransitionProgressReanimated being \"fake\" prop for parsing of `useEvent` return value\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\n\nReanimatedScreen.displayName = 'ReanimatedScreen';\n\nexport default ReanimatedScreen;\n"]}
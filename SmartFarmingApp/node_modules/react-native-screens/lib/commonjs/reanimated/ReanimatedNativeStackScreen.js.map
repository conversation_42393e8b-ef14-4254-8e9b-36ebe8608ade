{"version": 3, "sources": ["ReanimatedNativeStackScreen.tsx"], "names": ["AnimatedScreen", "Animated", "createAnimatedComponent", "InnerScreen", "ENABLE_FABRIC", "global", "_IS_FABRIC", "ReanimatedNativeStackScreen", "React", "forwardRef", "props", "ref", "children", "rest", "progress", "closing", "goingForward", "event", "value", "Platform", "OS", "displayName"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AAOA;;AACA;;;;;;;;;;;;AAEA,MAAMA,cAAc,GAAGC,+BAASC,uBAAT,CACpBC,+BADoB,CAAvB,C,CAIA;AACA;AACA;;;AACA,MAAMC,aAAa,GAAG,CAAC,aAACC,MAAD,oCAAC,QAAQC,UAAT,CAAvB;;AAEA,MAAMC,2BAA2B,gBAAGC,eAAMC,UAAN,CAGlC,CAACC,KAAD,EAAQC,GAAR,KAAgB;AAChB,QAAM;AAAEC,IAAAA,QAAF;AAAY,OAAGC;AAAf,MAAwBH,KAA9B;AAEA,QAAMI,QAAQ,GAAG,2CAAe,CAAf,CAAjB;AACA,QAAMC,OAAO,GAAG,2CAAe,CAAf,CAAhB;AACA,QAAMC,YAAY,GAAG,2CAAe,CAAf,CAArB;AAEA,sBACE,6BAAC,cAAD,CACE;AADF;AAEE,IAAA,GAAG,EAAEL,GAFP;AAGE,IAAA,8BAA8B,EAAE,qCAC7BM,KAAD,IAAwC;AACtC;;AACAH,MAAAA,QAAQ,CAACI,KAAT,GAAiBD,KAAK,CAACH,QAAvB;AACAC,MAAAA,OAAO,CAACG,KAAR,GAAgBD,KAAK,CAACF,OAAtB;AACAC,MAAAA,YAAY,CAACE,KAAb,GAAqBD,KAAK,CAACD,YAA3B;AACD,KAN6B,EAO9B,CACE;AACA;AACAG,0BAASC,EAAT,KAAgB,SAAhB,GACI,sBADJ,GAEI;AACFhB,IAAAA,aAAa,GACX,sBADW,GAEX,uBARN,CAP8B;AAHlC,KAqBMS,IArBN,gBAsBE,6BAAC,4CAAD,CAAqC,QAArC;AACE,IAAA,KAAK,EAAE;AACLC,MAAAA,QAAQ,EAAEA,QADL;AAELC,MAAAA,OAAO,EAAEA,OAFJ;AAGLC,MAAAA,YAAY,EAAEA;AAHT;AADT,KAMGJ,QANH,CAtBF,CADF;AAiCD,CA3CmC,CAApC;;AA6CAL,2BAA2B,CAACc,WAA5B,GAA0C,6BAA1C;eAEed,2B", "sourcesContent": ["import React from 'react';\nimport { Platform } from 'react-native';\nimport {\n  InnerScreen,\n  ScreenProps,\n  TransitionProgressEventType,\n} from 'react-native-screens';\n\n// @ts-ignore file to be used only if `react-native-reanimated` available in the project\nimport Animated, { useEvent, useSharedValue } from 'react-native-reanimated';\nimport ReanimatedTransitionProgressContext from './ReanimatedTransitionProgressContext';\n\nconst AnimatedScreen = Animated.createAnimatedComponent(\n  (InnerScreen as unknown) as React.ComponentClass\n);\n\n// We use prop added to global by reanimated since it seems safer than the one from RN. See:\n// https://github.com/software-mansion/react-native-reanimated/blob/3fe8b35b05e82b2f2aefda1fb97799cf81e4b7bb/src/reanimated2/UpdateProps.ts#L46\n// @ts-expect-error nativeFabricUIManager is not yet included in the RN types\nconst ENABLE_FABRIC = !!global?._IS_FABRIC;\n\nconst ReanimatedNativeStackScreen = React.forwardRef<\n  typeof AnimatedScreen,\n  ScreenProps\n>((props, ref) => {\n  const { children, ...rest } = props;\n\n  const progress = useSharedValue(0);\n  const closing = useSharedValue(0);\n  const goingForward = useSharedValue(0);\n\n  return (\n    <AnimatedScreen\n      // @ts-ignore some problems with ref and onTransitionProgressReanimated being \"fake\" prop for parsing of `useEvent` return value\n      ref={ref}\n      onTransitionProgressReanimated={useEvent(\n        (event: TransitionProgressEventType) => {\n          'worklet';\n          progress.value = event.progress;\n          closing.value = event.closing;\n          goingForward.value = event.goingForward;\n        },\n        [\n          // This should not be necessary, but is not properly managed by `react-native-reanimated`\n          // @ts-ignore wrong type\n          Platform.OS === 'android'\n            ? 'onTransitionProgress'\n            : // for some reason there is a difference in required event name between architectures\n            ENABLE_FABRIC\n            ? 'onTransitionProgress'\n            : 'topTransitionProgress',\n        ]\n      )}\n      {...rest}>\n      <ReanimatedTransitionProgressContext.Provider\n        value={{\n          progress: progress,\n          closing: closing,\n          goingForward: goingForward,\n        }}>\n        {children}\n      </ReanimatedTransitionProgressContext.Provider>\n    </AnimatedScreen>\n  );\n});\n\nReanimatedNativeStackScreen.displayName = 'ReanimatedNativeStackScreen';\n\nexport default ReanimatedNativeStackScreen;\n"]}
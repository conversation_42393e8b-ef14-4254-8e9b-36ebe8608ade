{"version": 3, "sources": ["useReanimatedTransitionProgress.tsx"], "names": ["useReanimatedTransitionProgress", "progress", "React", "useContext", "ReanimatedTransitionProgressContext", "undefined", "Error"], "mappings": ";;;;;;;AAAA;;AACA;;;;;;;;AAEe,SAASA,+BAAT,GAA2C;AACxD,QAAMC,QAAQ,GAAGC,KAAK,CAACC,UAAN,CAAiBC,4CAAjB,CAAjB;;AAEA,MAAIH,QAAQ,KAAKI,SAAjB,EAA4B;AAC1B,UAAM,IAAIC,KAAJ,CACJ,mGADI,CAAN;AAGD;;AAED,SAAOL,QAAP;AACD", "sourcesContent": ["import * as React from 'react';\nimport ReanimatedTransitionProgressContext from './ReanimatedTransitionProgressContext';\n\nexport default function useReanimatedTransitionProgress() {\n  const progress = React.useContext(ReanimatedTransitionProgressContext);\n\n  if (progress === undefined) {\n    throw new Error(\n      \"Couldn't find values for reanimated transition progress. Are you inside a screen in Native Stack?\"\n    );\n  }\n\n  return progress;\n}\n"]}
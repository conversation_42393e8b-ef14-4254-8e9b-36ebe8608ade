{"version": 3, "sources": ["ScreenStackHeaderSubviewNativeComponent.js"], "names": [], "mappings": ";;;;;;;AAKA;;AAEA;;;;;;;;AAPA;AACA;AACA;AACA;;AACA;eAuBgB,qCACd,6BADc,EAEd,EAFc,C", "sourcesContent": ["/**\n * @flow strict-local\n * @format\n */\n/* eslint-disable */\nimport * as React from 'react';\n\nimport codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\nimport type { ViewProps } from 'react-native/Libraries/Components/View/ViewPropTypes';\nimport type { HostComponent } from 'react-native/Libraries/Renderer/shims/ReactNativeTypes';\nimport type { WithDefault } from 'react-native/Libraries/Types/CodegenTypes';\n\nexport type HeaderSubviewTypes =\n  | 'back'\n  | 'right'\n  | 'left'\n  | 'title'\n  | 'center'\n  | 'searchBar';\n\nexport type NativeProps = $ReadOnly<{|\n  ...ViewProps,\n  type?: WithDefault<HeaderSubviewTypes, 'left'>,\n|}>;\n\ntype ComponentType = HostComponent<NativeProps>;\n\nexport default (codegenNativeComponent<NativeProps>(\n  'RNSScreenStackHeaderSubview',\n  {}\n): ComponentType);\n"]}
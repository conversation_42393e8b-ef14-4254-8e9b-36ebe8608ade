{"version": 3, "sources": ["ScreenNativeComponent.js"], "names": ["interfaceOnly"], "mappings": ";;;;;;;AAKA;;;;AALA;AACA;AACA;AACA;;AACA;eA0FgB,qCAAoC,WAApC,EAAiD;AAC/DA,EAAAA,aAAa,EAAE;AADgD,CAAjD,C", "sourcesContent": ["/**\n * @flow strict-local\n * @format\n */\n/* eslint-disable */\nimport codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\nimport type { ViewProps } from 'react-native/Libraries/Components/View/ViewPropTypes';\nimport type { HostComponent } from 'react-native/Libraries/Renderer/shims/ReactNativeTypes';\nimport type { ColorValue } from 'react-native/Libraries/StyleSheet/StyleSheet';\nimport type {\n  BubblingEventHandler,\n  WithDefault,\n  Int32,\n} from 'react-native/Libraries/Types/CodegenTypes';\n\ntype ScreenEvent = $ReadOnly<{||}>;\n\ntype ScreenDismissedEvent = $ReadOnly<{|\n  dismissCount: Int32,\n|}>;\n\ntype TransitionProgressEvent = $ReadOnly<{|\n  progress: Double,\n  closing: Int32,\n  goingForward: Int32,\n|}>;\n\ntype GestureResponseDistanceType = $ReadOnly<{|\n  start: Float,\n  end: Float,\n  top: Float,\n  bottom: Float,\n|}>;\n\ntype StackPresentation =\n  | 'push'\n  | 'modal'\n  | 'transparentModal'\n  | 'fullScreenModal'\n  | 'formSheet'\n  | 'containedModal'\n  | 'containedTransparentModal';\n\ntype StackAnimation =\n  | 'default'\n  | 'flip'\n  | 'simple_push'\n  | 'none'\n  | 'fade'\n  | 'slide_from_right'\n  | 'slide_from_left'\n  | 'slide_from_bottom'\n  | 'fade_from_bottom';\n\ntype SwipeDirection = 'vertical' | 'horizontal';\n\ntype ReplaceAnimation = 'pop' | 'push';\n\nexport type NativeProps = $ReadOnly<{|\n  ...ViewProps,\n  onAppear?: ?BubblingEventHandler<ScreenEvent>,\n  onDisappear?: ?BubblingEventHandler<ScreenEvent>,\n  onDismissed?: ?BubblingEventHandler<ScreenDismissedEvent>,\n  onNativeDismissCancelled?: ?BubblingEventHandler<ScreenDismissedEvent>,\n  onWillAppear?: ?BubblingEventHandler<ScreenEvent>,\n  onWillDisappear?: ?BubblingEventHandler<ScreenEvent>,\n  onTransitionProgress?: ?BubblingEventHandler<TransitionProgressEvent>,\n  customAnimationOnSwipe?: boolean,\n  fullScreenSwipeEnabled?: boolean,\n  homeIndicatorHidden?: boolean,\n  preventNativeDismiss?: boolean,\n  gestureEnabled?: WithDefault<boolean, true>,\n  statusBarColor?: ColorValue,\n  statusBarHidden?: boolean,\n  screenOrientation?: string,\n  statusBarAnimation?: string,\n  statusBarStyle?: string,\n  statusBarTranslucent?: boolean,\n  gestureResponseDistance?: GestureResponseDistanceType,\n  stackPresentation?: WithDefault<StackPresentation, 'push'>,\n  stackAnimation?: WithDefault<StackAnimation, 'default'>,\n  transitionDuration?: WithDefault<Int32, 350>,\n  replaceAnimation?: WithDefault<ReplaceAnimation, 'pop'>,\n  swipeDirection?: WithDefault<SwipeDirection, 'horizontal'>,\n  hideKeyboardOnSwipe?: boolean,\n  activityState?: WithDefault<Float, -1.0>,\n  navigationBarColor?: ColorValue,\n  navigationBarHidden?: boolean,\n  nativeBackButtonDismissalEnabled?: boolean,\n  onHeaderBackButtonClicked?: ?BubblingEventHandler<ScreenEvent>,\n|}>;\n\ntype ComponentType = HostComponent<NativeProps>;\n\nexport default (codegenNativeComponent<NativeProps>('RNSScreen', {\n  interfaceOnly: true,\n}): ComponentType);\n"]}
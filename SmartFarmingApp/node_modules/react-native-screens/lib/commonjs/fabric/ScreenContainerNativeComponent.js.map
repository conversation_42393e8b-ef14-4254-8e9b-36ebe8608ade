{"version": 3, "sources": ["ScreenContainerNativeComponent.js"], "names": [], "mappings": ";;;;;;;AAKA;;;;AALA;AACA;AACA;AACA;;AACA;eAWgB,qCACd,oBADc,EAEd,EAFc,C", "sourcesContent": ["/**\n * @flow strict-local\n * @format\n */\n/* eslint-disable */\nimport codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\nimport type { ViewProps } from 'react-native/Libraries/Components/View/ViewPropTypes';\nimport type { HostComponent } from 'react-native/Libraries/Renderer/shims/ReactNativeTypes';\n\ntype NativeProps = $ReadOnly<{|\n  ...ViewProps,\n|}>;\n\ntype ComponentType = HostComponent<NativeProps>;\n\nexport default (codegenNativeComponent<NativeProps>(\n  'RNSScreenContainer',\n  {}\n): ComponentType);\n"]}
"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _codegenNativeComponent = _interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeComponent"));

var _StyleSheet = require("react-native/Libraries/StyleSheet/StyleSheet");

var _ReactNativeStyleAttributes = require("react-native/Libraries/Components/View/ReactNativeStyleAttributes");

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

/**
 * 
 * @format
 */

/* eslint-disable */
var _default = (0, _codegenNativeComponent.default)('RNSSearchBar', {});

exports.default = _default;
//# sourceMappingURL=SearchBarNativeComponent.js.map
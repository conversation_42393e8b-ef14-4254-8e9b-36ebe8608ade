{"version": 3, "sources": ["index.tsx"], "names": ["ENABLE_SCREENS", "enableScreens", "shouldEnableScreens", "screensEnabled", "enableFreeze", "shouldEnableReactFreeze", "NativeScreen", "React", "Component", "render", "active", "activityState", "style", "enabled", "rest", "props", "undefined", "display", "Screen", "Animated", "createAnimatedComponent", "InnerScreen", "View", "ScreenContext", "createContext", "ScreenContainer", "NativeScreenContainer", "NativeScreenNavigationContainer", "ScreenStack", "FullWindowOverlay", "ScreenStackHeaderBackButtonImage", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderSearchBarView", "ScreenStackHeaderConfig", "SearchBar", "ScreenStackHeaderSubview", "shouldUseActivityState"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACA;;AAUA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;;AACA;;;;;;AAKA,IAAIA,cAAc,GAAG,IAArB;;AAEO,SAASC,aAAT,CAAuBC,mBAAmB,GAAG,IAA7C,EAAyD;AAC9DF,EAAAA,cAAc,GAAGE,mBAAjB;AACD;;AAEM,SAASC,cAAT,GAAmC;AACxC,SAAOH,cAAP;AACD,C,CAED;AACA;;;AACO,SAASI,YAAT,CAAsBC,uBAAuB,GAAG,IAAhD,EAA4D,CACjE;AACD;;AAEM,MAAMC,YAAN,SAA2BC,eAAMC,SAAjC,CAAwD;AAC7DC,EAAAA,MAAM,GAAgB;AACpB,QAAI;AACFC,MAAAA,MADE;AAEFC,MAAAA,aAFE;AAGFC,MAAAA,KAHE;AAIFC,MAAAA,OAAO,GAAGb,cAJR;AAKF,SAAGc;AALD,QAMA,KAAKC,KANT;;AAQA,QAAIF,OAAJ,EAAa;AACX,UAAIH,MAAM,KAAKM,SAAX,IAAwBL,aAAa,KAAKK,SAA9C,EAAyD;AACvDL,QAAAA,aAAa,GAAGD,MAAM,KAAK,CAAX,GAAe,CAAf,GAAmB,CAAnC,CADuD,CACjB;AACvC;;AACD,0BACE,6BAAC,iBAAD,CACE;AADF;AAEE,QAAA,MAAM,EAAEC,aAAa,KAAK,CAF5B;AAGE,QAAA,KAAK,EAAE,CAACC,KAAD,EAAQ;AAAEK,UAAAA,OAAO,EAAEN,aAAa,KAAK,CAAlB,GAAsB,MAAtB,GAA+B;AAA1C,SAAR;AAHT,SAIMG,IAJN,EADF;AAQD;;AAED,wBAAO,6BAAC,iBAAD,EAAUA,IAAV,CAAP;AACD;;AAzB4D;;;;AA4BxD,MAAMI,MAAM,GAAGC,sBAASC,uBAAT,CAAiCd,YAAjC,CAAf;;;AAEA,MAAMe,WAAW,GAAGC,iBAApB;;;AAEA,MAAMC,aAAa,gBAAGhB,eAAMiB,aAAN,CAAoBN,MAApB,CAAtB;;;AAEA,MAAMO,eAA0D,GAAGH,iBAAnE;;AAEA,MAAMI,qBAAgE,GAAGJ,iBAAzE;;AAEA,MAAMK,+BAA0E,GAAGL,iBAAnF;;AAEA,MAAMM,WAAkD,GAAGN,iBAA3D;;AAEA,MAAMO,iBAAiB,GAAGP,iBAA1B;;;AAEA,MAAMQ,gCAAgC,GAC3Cf,KAD8C,iBAG9C,6BAAC,iBAAD,qBACE,6BAAC,kBAAD;AAAO,EAAA,UAAU,EAAC,QAAlB;AAA2B,EAAA,YAAY,EAAE;AAAzC,GAAgDA,KAAhD,EADF,CAHK;;;;AAQA,MAAMgB,0BAA0B,GACrChB,KADwC,iBAExB,6BAAC,iBAAD,EAAUA,KAAV,CAFX;;;;AAIA,MAAMiB,yBAAyB,GACpCjB,KADuC,iBAEvB,6BAAC,iBAAD,EAAUA,KAAV,CAFX;;;;AAIA,MAAMkB,2BAA2B,GACtClB,KADyC,iBAEzB,6BAAC,iBAAD,EAAUA,KAAV,CAFX;;;;AAIA,MAAMmB,8BAA8B,GACzCnB,KAD4C,iBAE5B,6BAAC,iBAAD,EAAUA,KAAV,CAFX;;;AAIA,MAAMoB,uBAA0E,GAAGb,iBAAnF,C,CAEP;;;AACO,MAAMc,SAA8C,GAAGd,iBAAvD;;AAEA,MAAMe,wBAEX,GAAGf,iBAFE;;AAIA,MAAMgB,sBAAsB,GAAG,IAA/B", "sourcesContent": ["import React from 'react';\nimport { Animated, View, ViewProps, ImageProps, Image } from 'react-native';\nimport {\n  ScreenProps,\n  ScreenContainerProps,\n  ScreenStackProps,\n  ScreenStackHeaderConfigProps,\n  HeaderSubviewTypes,\n  SearchBarProps,\n} from './types';\n\nexport * from './types';\nexport { default as useTransitionProgress } from './useTransitionProgress';\nexport {\n  isSearchBarAvailableForCurrentPlatform,\n  executeNativeBackPress,\n} from './utils';\n\nlet ENABLE_SCREENS = true;\n\nexport function enableScreens(shouldEnableScreens = true): void {\n  ENABLE_SCREENS = shouldEnableScreens;\n}\n\nexport function screensEnabled(): boolean {\n  return ENABLE_SCREENS;\n}\n\n// @ts-ignore function stub, freezing logic is located in index.native.tsx\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function enableFreeze(shouldEnableReactFreeze = true): void {\n  // noop\n}\n\nexport class NativeScreen extends React.Component<ScreenProps> {\n  render(): JSX.Element {\n    let {\n      active,\n      activityState,\n      style,\n      enabled = ENABLE_SCREENS,\n      ...rest\n    } = this.props;\n\n    if (enabled) {\n      if (active !== undefined && activityState === undefined) {\n        activityState = active !== 0 ? 2 : 0; // change taken from index.native.tsx\n      }\n      return (\n        <View\n          // @ts-expect-error: hidden exists on web, but not in React Native\n          hidden={activityState === 0}\n          style={[style, { display: activityState !== 0 ? 'flex' : 'none' }]}\n          {...rest}\n        />\n      );\n    }\n\n    return <View {...rest} />;\n  }\n}\n\nexport const Screen = Animated.createAnimatedComponent(NativeScreen);\n\nexport const InnerScreen = View;\n\nexport const ScreenContext = React.createContext(Screen);\n\nexport const ScreenContainer: React.ComponentType<ScreenContainerProps> = View;\n\nexport const NativeScreenContainer: React.ComponentType<ScreenContainerProps> = View;\n\nexport const NativeScreenNavigationContainer: React.ComponentType<ScreenContainerProps> = View;\n\nexport const ScreenStack: React.ComponentType<ScreenStackProps> = View;\n\nexport const FullWindowOverlay = View;\n\nexport const ScreenStackHeaderBackButtonImage = (\n  props: ImageProps\n): JSX.Element => (\n  <View>\n    <Image resizeMode=\"center\" fadeDuration={0} {...props} />\n  </View>\n);\n\nexport const ScreenStackHeaderRightView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderLeftView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderCenterView = (\n  props: React.PropsWithChildren<ViewProps>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderSearchBarView = (\n  props: React.PropsWithChildren<SearchBarProps>\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderConfig: React.ComponentType<ScreenStackHeaderConfigProps> = View;\n\n// @ts-expect-error: search bar props have no common props with View\nexport const SearchBar: React.ComponentType<SearchBarProps> = View;\n\nexport const ScreenStackHeaderSubview: React.ComponentType<React.PropsWithChildren<\n  ViewProps & { type?: HeaderSubviewTypes }\n>> = View;\n\nexport const shouldUseActivityState = true;\n"]}
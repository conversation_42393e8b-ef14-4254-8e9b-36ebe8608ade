import React from 'react';
import type { GestureProviderProps } from 'src/native-stack/types';
declare const ScreenGestureDetector: ({ children, gestureDetectorBridge, goBackGesture, screenEdgeGesture, transitionAnimation: customTransitionAnimation, screensRefs, currentRouteKey, }: GestureProviderProps) => React.JSX.Element;
export default ScreenGestureDetector;
//# sourceMappingURL=ScreenGestureDetector.d.ts.map
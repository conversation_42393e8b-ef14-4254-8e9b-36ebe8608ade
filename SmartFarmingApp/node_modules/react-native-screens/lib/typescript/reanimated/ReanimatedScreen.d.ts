import React from 'react';
import { ScreenProps } from 'react-native-screens';
import Animated from 'react-native-reanimated';
declare const ReanimatedScreen: React.ForwardRefExoticComponent<Pick<ScreenProps, "children" | "active" | "activityState" | "customAnimationOnSwipe" | "enabled" | "isNativeStack" | "freezeOnBlur" | "fullScreenSwipeEnabled" | "gestureEnabled" | "gestureResponseDistance" | "homeIndicatorHidden" | "hideKeyboardOnSwipe" | "nativeBackButtonDismissalEnabled" | "navigationBarColor" | "navigationBarHidden" | "onAppear" | "onComponentRef" | "onDisappear" | "onDismissed" | "onHeaderBackButtonClicked" | "onNativeDismissCancelled" | "onTransitionProgress" | "onWillAppear" | "onWillDisappear" | "preventNativeDismiss" | "replaceAnimation" | "screenOrientation" | "stackAnimation" | "stackPresentation" | "statusBarAnimation" | "statusBarColor" | "statusBarHidden" | "statusBarStyle" | "statusBarTranslucent" | "swipeDirection" | "transitionDuration" | "hitSlop" | "onLayout" | "pointerEvents" | "removeClippedSubviews" | "style" | "testID" | "nativeID" | "collapsable" | "needsOffscreenAlphaCompositing" | "renderToHardwareTextureAndroid" | "focusable" | "shouldRasterizeIOS" | "isTVSelectable" | "hasTVPreferredFocus" | "tvParallaxProperties" | "tvParallaxShiftDistanceX" | "tvParallaxShiftDistanceY" | "tvParallaxTiltAngle" | "tvParallaxMagnification" | "onStartShouldSetResponder" | "onMoveShouldSetResponder" | "onResponderEnd" | "onResponderGrant" | "onResponderReject" | "onResponderMove" | "onResponderRelease" | "onResponderStart" | "onResponderTerminationRequest" | "onResponderTerminate" | "onStartShouldSetResponderCapture" | "onMoveShouldSetResponderCapture" | "onTouchStart" | "onTouchMove" | "onTouchEnd" | "onTouchCancel" | "onTouchEndCapture" | "accessible" | "accessibilityActions" | "accessibilityLabel" | "accessibilityRole" | "accessibilityState" | "accessibilityHint" | "accessibilityValue" | "onAccessibilityAction" | "accessibilityComponentType" | "accessibilityLiveRegion" | "importantForAccessibility" | "accessibilityElementsHidden" | "accessibilityTraits" | "accessibilityViewIsModal" | "onAccessibilityEscape" | "onAccessibilityTap" | "onMagicTap" | "accessibilityIgnoresInvertColors"> & React.RefAttributes<React.ComponentClass<Animated.AnimateProps<{}>, any>>>;
export default ReanimatedScreen;

{"name": "react-native-screens", "version": "3.18.2", "description": "Native navigation primitives for your React Native app.", "scripts": {"check-types": "tsc --noEmit", "start": "react-native start", "test:unit": "jest --passWithNoTests", "format-js": "prettier --write --list-different './{src,Example}/**/*.{js,ts,tsx}'", "format-android": "node ./scripts/format-android.js", "format-ios": "find ios/ -iname *.h -o -iname *.m -o -iname *.cpp -o -iname *.mm | xargs clang-format -i", "format": "yarn format-js && yarn format-android && yarn format-ios", "lint-js": "eslint --ext '.js,.ts,.tsx' --fix src", "lint-android": "./android/gradlew -p android spotlessCheck -q", "lint": "yarn lint-js && yarn lint-android", "release": "npm login && release-it", "prepare": "bob build && husky install"}, "main": "lib/commonjs/index", "module": "lib/module/index", "react-native": "src/index", "source": "src/index", "types": "lib/typescript/index", "files": ["src/", "common/", "lib/", "native-stack/", "createNativeStackNavigator/", "reanimated/", "android/src/main/AndroidManifest.xml", "android/src/main/java/", "android/src/main/jni/", "android/src/main/res", "android/src/fabric/", "android/src/paper/", "android/build.gradle", "ios/", "windows/", "RNScreens.podspec", "react-native.config.js", "README.md", "!**/__tests__", "!android/src/main/java/com/swmansion/rnscreens/LifecycleHelper.kt"], "repository": {"type": "git", "url": "git+https://github.com/software-mansion/react-native-screens.git"}, "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/software-mansion/react-native-screens/issues"}, "homepage": "https://github.com/software-mansion/react-native-screens#readme", "dependencies": {"react-freeze": "^1.0.0", "warn-once": "^0.1.0"}, "peerDependencies": {"react": "*", "react-native": "*"}, "devDependencies": {"@babel/core": "^7.11.0", "@react-native-community/bob": "^0.17.1", "@react-navigation/native": "^5.8.0", "@react-navigation/stack": "^5.10.0", "@types/jest": "^26.0.8", "@types/react": "^16.9.44", "@types/react-native": "^0.63.2", "@types/react-test-renderer": "^16.9.2", "@typescript-eslint/eslint-plugin": "^3.7.1", "@typescript-eslint/parser": "^3.7.1", "babel-eslint": "^10.1.0", "babel-jest": "^26.2.2", "clang-format": "^1.5.0", "eslint": "^7.6.0", "eslint-config-prettier": "^6.11.0", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-react": "^7.20.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-react-native": "^3.2.1", "eslint-plugin-standard": "^4.0.1", "husky": "^7.0.1", "jest": "^26.2.2", "jest-react-native": "18.0.0", "lint-staged": "^11.1.2", "metro-react-native-babel-preset": "^0.61.0", "prettier": "^2.0.4", "react": "^16.13.1", "react-dom": "^16.13.1", "react-native": "^0.63.2", "react-native-reanimated": "^2.2.0", "react-native-safe-area-context": "^4.0.1-rc.5", "react-native-windows": "^0.64.8", "react-navigation": "^4.4.3", "react-navigation-stack": "^2.9.0", "react-test-renderer": "^16.13.1", "release-it": "^13.5.2", "typescript": "^3.9.7"}, "lint-staged": {"{src,Example}/**/*.{js,ts,tsx}": "yarn format-js", "src/**/*.{js,ts,tsx}": "yarn lint-js", "android/**/*.kt": "yarn format-android", "ios/**/*.{h,m,mm,cpp}": "yarn format-ios"}, "@react-native-community/bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.build.json"}]]}, "eslintIgnore": ["node_modules/", "lib/"], "codegenConfig": {"name": "rnscreens", "type": "components", "jsSrcsDir": "./src/fabric", "android": {"javaPackageName": "com.swmansion.rnscreens"}}}
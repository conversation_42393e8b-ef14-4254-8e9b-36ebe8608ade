  Boolean com.facebook.react.model  List com.facebook.react.model  Map com.facebook.react.model  "ModelAutolinkingAndroidProjectJson com.facebook.react.model  ModelAutolinkingConfigJson com.facebook.react.model   ModelAutolinkingDependenciesJson com.facebook.react.model  /ModelAutolinkingDependenciesPlatformAndroidJson com.facebook.react.model  (ModelAutolinkingDependenciesPlatformJson com.facebook.react.model  ModelAutolinkingProjectJson com.facebook.react.model  ModelCodegenConfig com.facebook.react.model  ModelCodegenConfigAndroid com.facebook.react.model  ModelPackageJson com.facebook.react.model  Regex com.facebook.react.model  String com.facebook.react.model  	emptyList com.facebook.react.model  replace com.facebook.react.model  Regex 9com.facebook.react.model.ModelAutolinkingDependenciesJson  name 9com.facebook.react.model.ModelAutolinkingDependenciesJson  replace 9com.facebook.react.model.ModelAutolinkingDependenciesJson  Any com.facebook.react.utils  Boolean com.facebook.react.utils  	Character com.facebook.react.utils  File com.facebook.react.utils  Gson com.facebook.react.utils  	JsonUtils com.facebook.react.utils  KotlinStdlibCompatUtils com.facebook.react.utils  List com.facebook.react.utils  Locale com.facebook.react.utils  ModelAutolinkingConfigJson com.facebook.react.utils  ModelPackageJson com.facebook.react.utils  Os com.facebook.react.utils  String com.facebook.react.utils  Suppress com.facebook.react.utils  System com.facebook.react.utils  bufferedReader com.facebook.react.utils  contains com.facebook.react.utils  	filterNot com.facebook.react.utils  
isNotEmpty com.facebook.react.utils  	isWindows com.facebook.react.utils  java com.facebook.react.utils  joinToString com.facebook.react.utils  let com.facebook.react.utils  listOf com.facebook.react.utils  lowercaseCompat com.facebook.react.utils  plus com.facebook.react.utils  	readLines com.facebook.react.utils  
relativeTo com.facebook.react.utils  replace com.facebook.react.utils  runCatching com.facebook.react.utils  
startsWith com.facebook.react.utils  	substring com.facebook.react.utils  toList com.facebook.react.utils  trim com.facebook.react.utils  use com.facebook.react.utils  windowsAwareBashCommandLine com.facebook.react.utils  windowsAwareCommandLine com.facebook.react.utils  Gson "com.facebook.react.utils.JsonUtils  ModelAutolinkingConfigJson "com.facebook.react.utils.JsonUtils  ModelPackageJson "com.facebook.react.utils.JsonUtils  bufferedReader "com.facebook.react.utils.JsonUtils  	filterNot "com.facebook.react.utils.JsonUtils  
gsonConverter "com.facebook.react.utils.JsonUtils  java "com.facebook.react.utils.JsonUtils  joinToString "com.facebook.react.utils.JsonUtils  	readLines "com.facebook.react.utils.JsonUtils  runCatching "com.facebook.react.utils.JsonUtils  
startsWith "com.facebook.react.utils.JsonUtils  trim "com.facebook.react.utils.JsonUtils  use "com.facebook.react.utils.JsonUtils  	Character 0com.facebook.react.utils.KotlinStdlibCompatUtils  Locale 0com.facebook.react.utils.KotlinStdlibCompatUtils  
isNotEmpty 0com.facebook.react.utils.KotlinStdlibCompatUtils  lowercaseCompat 0com.facebook.react.utils.KotlinStdlibCompatUtils  plus 0com.facebook.react.utils.KotlinStdlibCompatUtils  	substring 0com.facebook.react.utils.KotlinStdlibCompatUtils  System com.facebook.react.utils.Os  contains com.facebook.react.utils.Os  	isWindows com.facebook.react.utils.Os  let com.facebook.react.utils.Os  lowercaseCompat com.facebook.react.utils.Os  
relativeTo com.facebook.react.utils.Os  replace com.facebook.react.utils.Os  
startsWith com.facebook.react.utils.Os  lang com.facebook.react.utils.java  String "com.facebook.react.utils.java.lang  Gson com.google.gson  fromJson com.google.gson.Gson  BufferedReader java.io  File java.io  	readLines java.io.BufferedReader  use java.io.BufferedReader  absolutePath java.io.File  bufferedReader java.io.File  	isWindows java.io.File  path java.io.File  
relativeTo java.io.File  Class 	java.lang  String 	java.lang  toUpperCase java.lang.Character  toLowerCase java.lang.String  getProperty java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  Locale 	java.util  ROOT java.util.Locale  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function1 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  Pair kotlin  Result kotlin  
ShortArray kotlin  Suppress kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  let kotlin  plus kotlin  runCatching kotlin  toList kotlin  use kotlin  toList kotlin.Array  not kotlin.Boolean  plus kotlin.Char  	getOrNull 
kotlin.Result  	Character 
kotlin.String  contains 
kotlin.String  get 
kotlin.String  
isNotEmpty 
kotlin.String  let 
kotlin.String  lowercaseCompat 
kotlin.String  plus 
kotlin.String  replace 
kotlin.String  
startsWith 
kotlin.String  	substring 
kotlin.String  trim 
kotlin.String  List kotlin.collections  Map kotlin.collections  Set kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  	filterNot kotlin.collections  
isNotEmpty kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  plus kotlin.collections  toList kotlin.collections  	filterNot kotlin.collections.List  joinToString kotlin.collections.List  plus kotlin.collections.List  bufferedReader 	kotlin.io  	readLines 	kotlin.io  
relativeTo 	kotlin.io  
startsWith 	kotlin.io  use 	kotlin.io  java 
kotlin.jvm  contains 
kotlin.ranges  java kotlin.reflect.KClass  Sequence kotlin.sequences  contains kotlin.sequences  	filterNot kotlin.sequences  joinToString kotlin.sequences  plus kotlin.sequences  toList kotlin.sequences  Regex kotlin.text  contains kotlin.text  	filterNot kotlin.text  
isNotEmpty kotlin.text  plus kotlin.text  replace kotlin.text  
startsWith kotlin.text  	substring kotlin.text  toList kotlin.text  trim kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   
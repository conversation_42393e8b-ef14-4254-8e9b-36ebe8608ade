-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:1:1-26:12
MERGED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:1:1-26:12
INJECTED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml:2:1-9:12
INJECTED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml:2:1-9:12
INJECTED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-safe-area-context] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.80.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/89ee2d71482aa90a74abf94579aa4b78/transformed/react-android-0.80.0-debug/AndroidManifest.xml:2:1-24:12
MERGED from [com.google.android.material:material:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/e547b5f8da205076801662ac08df6a3b/transformed/material-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/030bb4fced78a40c9362a571b6d348e4/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a10d6bace855d81fbf7e181d5806f81b/transformed/appcompat-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/4e48c2f552bc7d8c60d8fd5a0d9a5762/transformed/viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] /Users/<USER>/.gradle/caches/8.14.1/transforms/9aa2942444ebbf354e75dcfbb0c9e6c7/transformed/fragment-1.5.4/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/dca473709d0f85a81f330af717427a99/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/8015edec7970ba8a86e0fb3fbe6aa47d/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/b3cdd4970edfdf0df7c370a21c248251/transformed/autofill-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/445cb6b4e1fbaec36e7237098c7b7e92/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/0e0d7c35a0c8195aa480a1417b6d0bb3/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bc6bd16aedb34aeb0caf4121e982166e/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/43a35ddb782b9d49b1903d4d5d64acbf/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/7fc08eba5d623318bde5938ecd720a6a/transformed/transition-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/e0f7e772cc90199c7a5903a2ee428f3f/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/47f2b1cc1cb762399b2861eaf96c430d/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/244fcd4dd8adc9654278c798d5b4fce3/transformed/activity-1.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/2d4ee59b08440a4dba898701fcacfa72/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a0543b166d17217098251742806b43ab/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/0ba96735f22ca6813b0b41a8886331ce/transformed/fresco-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/334bb869d7b398ba997715d12479a2c0/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c700958cf60ff2c66286ce6b08f43353/transformed/drawee-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/fbae9e20e9bbc12deefe0c62697c9a10/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5b1de170db33b99a40d388f8a1eca969/transformed/memory-type-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bbf7d9f0d55445520fe6a2601b2c9f77/transformed/memory-type-java-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/1444749fa618364c8a4f5e5bd858c050/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/1c62183ac34bf8f4154bf4ac90cbde60/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d93ad321f960f3b76ef7704dd1de8733/transformed/imagepipeline-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/7c6ee6dd5204ab915d0472bb6011b85a/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/00a67b2134e2c63feb3dea2b5b9c9888/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/41d6d139c9d3fd61efb593adb973ada9/transformed/urimod-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f1e3b756a5951eab6b14036b35db430a/transformed/vito-source-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/ac93011594f9a5071ad89fe6f74c9d9c/transformed/middleware-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/727f0eb951e524a9b79a4b75ef6d7cbd/transformed/ui-common-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d07eb434a7e7dde796f1f6b68a729cc2/transformed/soloader-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/9fe7361379c5f863d60e707758def58f/transformed/fbcore-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/70d2605e048ff91afdba159d877fd8ea/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/769b9e40b38e07874fce96ed4f35c36f/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/da27c48a66cb5767699f92c7597d9202/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/4c59999035917766b230fc31671af6ab/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/9249bcc023740c2b0d39e1c225b9df22/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/3bee4b886bbbb037178986b0c7b7e3cb/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/d89bfc02dd875ef184732ad61affddd8/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/e54cd5a008ce7656b9026c811b495c3d/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/66dc8758e9aada5882404a25f960690f/transformed/core-ktx-1.16.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/2e63809a39e1994a3eb011d96a30ea4a/transformed/ui-core-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.80.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/67bc6afafe74fb80a28413c7f5b5c8cc/transformed/hermes-android-0.80.0-debug/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/03f11143426a417d78e14847e0e4aa48/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5b303c8e917ab0cdbd48111abbb0099f/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/aea8fd95f8687f8e6bae2098b767881a/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/32b305cd97b5e3701ce08afd677faa71/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/08e58a33b872996f4c0eb410fbbb15db/transformed/tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1ee30a6e15adc2e27062af0dffe1a553/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6fb248009c19d96db6f32347bb164bd7/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/51a65a7ea08b1391f45553157ea7fec5/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/174e020990892bb479738a1d732981a1/transformed/core-viewtree-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/5ee5ae3b232d9ddc46cf4eef335c0c7a/transformed/soloader-0.12.1/AndroidManifest.xml:2:1-17:12
	package
		INJECTED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:3:5-67
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:3:22-64
application
ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:5:5-25:19
MERGED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:5:5-25:19
MERGED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:5:5-25:19
INJECTED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml:5:5-8:50
MERGED from [com.facebook.react:react-android:0.80.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/89ee2d71482aa90a74abf94579aa4b78/transformed/react-android-0.80.0-debug/AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.80.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/89ee2d71482aa90a74abf94579aa4b78/transformed/react-android-0.80.0-debug/AndroidManifest.xml:18:5-22:19
MERGED from [com.google.android.material:material:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/e547b5f8da205076801662ac08df6a3b/transformed/material-1.1.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/e547b5f8da205076801662ac08df6a3b/transformed/material-1.1.0/AndroidManifest.xml:22:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a0543b166d17217098251742806b43ab/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a0543b166d17217098251742806b43ab/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/4c59999035917766b230fc31671af6ab/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/4c59999035917766b230fc31671af6ab/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/32b305cd97b5e3701ce08afd677faa71/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/32b305cd97b5e3701ce08afd677faa71/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1ee30a6e15adc2e27062af0dffe1a553/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1ee30a6e15adc2e27062af0dffe1a553/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/5ee5ae3b232d9ddc46cf4eef335c0c7a/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/5ee5ae3b232d9ddc46cf4eef335c0c7a/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:12:7-33
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:12:7-33
	android:label
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:7:7-39
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:7:7-39
	tools:ignore
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml:8:9-48
	android:roundIcon
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:9:7-52
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:9:7-52
	tools:targetApi
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml:7:9-29
	android:icon
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:8:7-41
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:8:7-41
	android:allowBackup
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:10:7-34
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:10:7-34
	android:theme
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:11:7-38
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:11:7-38
	android:usesCleartextTraffic
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml:6:9-44
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:6:7-38
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:6:7-38
activity#com.smartfarmingapp.MainActivity
ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:13:7-24:18
	android:label
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:15:9-41
	android:launchMode
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:17:9-40
	android:windowSoftInputMode
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:18:9-51
	android:exported
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:19:9-32
	android:configChanges
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:16:9-118
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:14:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:20:9-23:25
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:21:13-65
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:21:21-62
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:22:13-73
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/main/AndroidManifest.xml:22:23-70
uses-sdk
INJECTED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml
MERGED from [:react-native-safe-area-context] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.80.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/89ee2d71482aa90a74abf94579aa4b78/transformed/react-android-0.80.0-debug/AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.80.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/89ee2d71482aa90a74abf94579aa4b78/transformed/react-android-0.80.0-debug/AndroidManifest.xml:10:5-44
MERGED from [com.google.android.material:material:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/e547b5f8da205076801662ac08df6a3b/transformed/material-1.1.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/e547b5f8da205076801662ac08df6a3b/transformed/material-1.1.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/030bb4fced78a40c9362a571b6d348e4/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/030bb4fced78a40c9362a571b6d348e4/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a10d6bace855d81fbf7e181d5806f81b/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a10d6bace855d81fbf7e181d5806f81b/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/4e48c2f552bc7d8c60d8fd5a0d9a5762/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/4e48c2f552bc7d8c60d8fd5a0d9a5762/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] /Users/<USER>/.gradle/caches/8.14.1/transforms/9aa2942444ebbf354e75dcfbb0c9e6c7/transformed/fragment-1.5.4/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] /Users/<USER>/.gradle/caches/8.14.1/transforms/9aa2942444ebbf354e75dcfbb0c9e6c7/transformed/fragment-1.5.4/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/dca473709d0f85a81f330af717427a99/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/dca473709d0f85a81f330af717427a99/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/8015edec7970ba8a86e0fb3fbe6aa47d/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/8015edec7970ba8a86e0fb3fbe6aa47d/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/b3cdd4970edfdf0df7c370a21c248251/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/b3cdd4970edfdf0df7c370a21c248251/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/445cb6b4e1fbaec36e7237098c7b7e92/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/445cb6b4e1fbaec36e7237098c7b7e92/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/0e0d7c35a0c8195aa480a1417b6d0bb3/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/0e0d7c35a0c8195aa480a1417b6d0bb3/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bc6bd16aedb34aeb0caf4121e982166e/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bc6bd16aedb34aeb0caf4121e982166e/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/43a35ddb782b9d49b1903d4d5d64acbf/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/43a35ddb782b9d49b1903d4d5d64acbf/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/7fc08eba5d623318bde5938ecd720a6a/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/7fc08eba5d623318bde5938ecd720a6a/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/e0f7e772cc90199c7a5903a2ee428f3f/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/e0f7e772cc90199c7a5903a2ee428f3f/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/47f2b1cc1cb762399b2861eaf96c430d/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/47f2b1cc1cb762399b2861eaf96c430d/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/244fcd4dd8adc9654278c798d5b4fce3/transformed/activity-1.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/244fcd4dd8adc9654278c798d5b4fce3/transformed/activity-1.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/2d4ee59b08440a4dba898701fcacfa72/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/2d4ee59b08440a4dba898701fcacfa72/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a0543b166d17217098251742806b43ab/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a0543b166d17217098251742806b43ab/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/0ba96735f22ca6813b0b41a8886331ce/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/0ba96735f22ca6813b0b41a8886331ce/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/334bb869d7b398ba997715d12479a2c0/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/334bb869d7b398ba997715d12479a2c0/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c700958cf60ff2c66286ce6b08f43353/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c700958cf60ff2c66286ce6b08f43353/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/fbae9e20e9bbc12deefe0c62697c9a10/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/fbae9e20e9bbc12deefe0c62697c9a10/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5b1de170db33b99a40d388f8a1eca969/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5b1de170db33b99a40d388f8a1eca969/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bbf7d9f0d55445520fe6a2601b2c9f77/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bbf7d9f0d55445520fe6a2601b2c9f77/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/1444749fa618364c8a4f5e5bd858c050/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/1444749fa618364c8a4f5e5bd858c050/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/1c62183ac34bf8f4154bf4ac90cbde60/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/1c62183ac34bf8f4154bf4ac90cbde60/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d93ad321f960f3b76ef7704dd1de8733/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d93ad321f960f3b76ef7704dd1de8733/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/7c6ee6dd5204ab915d0472bb6011b85a/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/7c6ee6dd5204ab915d0472bb6011b85a/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/00a67b2134e2c63feb3dea2b5b9c9888/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/00a67b2134e2c63feb3dea2b5b9c9888/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/41d6d139c9d3fd61efb593adb973ada9/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/41d6d139c9d3fd61efb593adb973ada9/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f1e3b756a5951eab6b14036b35db430a/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f1e3b756a5951eab6b14036b35db430a/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/ac93011594f9a5071ad89fe6f74c9d9c/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/ac93011594f9a5071ad89fe6f74c9d9c/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/727f0eb951e524a9b79a4b75ef6d7cbd/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/727f0eb951e524a9b79a4b75ef6d7cbd/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d07eb434a7e7dde796f1f6b68a729cc2/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d07eb434a7e7dde796f1f6b68a729cc2/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/9fe7361379c5f863d60e707758def58f/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/9fe7361379c5f863d60e707758def58f/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/70d2605e048ff91afdba159d877fd8ea/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/70d2605e048ff91afdba159d877fd8ea/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/769b9e40b38e07874fce96ed4f35c36f/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/769b9e40b38e07874fce96ed4f35c36f/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/da27c48a66cb5767699f92c7597d9202/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/da27c48a66cb5767699f92c7597d9202/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/4c59999035917766b230fc31671af6ab/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/4c59999035917766b230fc31671af6ab/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/9249bcc023740c2b0d39e1c225b9df22/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/9249bcc023740c2b0d39e1c225b9df22/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/3bee4b886bbbb037178986b0c7b7e3cb/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/3bee4b886bbbb037178986b0c7b7e3cb/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/d89bfc02dd875ef184732ad61affddd8/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/d89bfc02dd875ef184732ad61affddd8/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/e54cd5a008ce7656b9026c811b495c3d/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/e54cd5a008ce7656b9026c811b495c3d/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/66dc8758e9aada5882404a25f960690f/transformed/core-ktx-1.16.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/66dc8758e9aada5882404a25f960690f/transformed/core-ktx-1.16.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/2e63809a39e1994a3eb011d96a30ea4a/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/2e63809a39e1994a3eb011d96a30ea4a/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.80.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/67bc6afafe74fb80a28413c7f5b5c8cc/transformed/hermes-android-0.80.0-debug/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.80.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/67bc6afafe74fb80a28413c7f5b5c8cc/transformed/hermes-android-0.80.0-debug/AndroidManifest.xml:5:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/03f11143426a417d78e14847e0e4aa48/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/03f11143426a417d78e14847e0e4aa48/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5b303c8e917ab0cdbd48111abbb0099f/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5b303c8e917ab0cdbd48111abbb0099f/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/aea8fd95f8687f8e6bae2098b767881a/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/aea8fd95f8687f8e6bae2098b767881a/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/32b305cd97b5e3701ce08afd677faa71/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/32b305cd97b5e3701ce08afd677faa71/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/08e58a33b872996f4c0eb410fbbb15db/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/08e58a33b872996f4c0eb410fbbb15db/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1ee30a6e15adc2e27062af0dffe1a553/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1ee30a6e15adc2e27062af0dffe1a553/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6fb248009c19d96db6f32347bb164bd7/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6fb248009c19d96db6f32347bb164bd7/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/51a65a7ea08b1391f45553157ea7fec5/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/51a65a7ea08b1391f45553157ea7fec5/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/174e020990892bb479738a1d732981a1/transformed/core-viewtree-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/174e020990892bb479738a1d732981a1/transformed/core-viewtree-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/3ba4db1ae352eb90610ca685a00c889a/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/5ee5ae3b232d9ddc46cf4eef335c0c7a/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/5ee5ae3b232d9ddc46cf4eef335c0c7a/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Documents/augment-projects/My-AI-Idea/SmartFarmingApp/android/app/src/debug/AndroidManifest.xml
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from [com.facebook.react:react-android:0.80.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/89ee2d71482aa90a74abf94579aa4b78/transformed/react-android-0.80.0-debug/AndroidManifest.xml:16:5-78
	android:name
		ADDED from [com.facebook.react:react-android:0.80.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/89ee2d71482aa90a74abf94579aa4b78/transformed/react-android-0.80.0-debug/AndroidManifest.xml:16:22-75
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.80.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/89ee2d71482aa90a74abf94579aa4b78/transformed/react-android-0.80.0-debug/AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.80.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/89ee2d71482aa90a74abf94579aa4b78/transformed/react-android-0.80.0-debug/AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.80.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/89ee2d71482aa90a74abf94579aa4b78/transformed/react-android-0.80.0-debug/AndroidManifest.xml:20:13-77
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a0543b166d17217098251742806b43ab/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/4c59999035917766b230fc31671af6ab/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/4c59999035917766b230fc31671af6ab/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/32b305cd97b5e3701ce08afd677faa71/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/32b305cd97b5e3701ce08afd677faa71/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a0543b166d17217098251742806b43ab/transformed/emoji2-1.3.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a0543b166d17217098251742806b43ab/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a0543b166d17217098251742806b43ab/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a0543b166d17217098251742806b43ab/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a0543b166d17217098251742806b43ab/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a0543b166d17217098251742806b43ab/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a0543b166d17217098251742806b43ab/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:23:9-81
permission#com.smartfarmingapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:26:22-94
uses-permission#com.smartfarmingapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6a8eda32708daa4f238d4c2804e6afbc/transformed/core-1.16.0/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/4c59999035917766b230fc31671af6ab/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/4c59999035917766b230fc31671af6ab/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/4c59999035917766b230fc31671af6ab/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/02b73e501bfcde42f4c6a528e186edeb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/5ee5ae3b232d9ddc46cf4eef335c0c7a/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/5ee5ae3b232d9ddc46cf4eef335c0c7a/transformed/soloader-0.12.1/AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/5ee5ae3b232d9ddc46cf4eef335c0c7a/transformed/soloader-0.12.1/AndroidManifest.xml:13:13-57
